import { 
  PinpointAnswer, 
  PinpointAnswerFormatted, 
  PinpointFormData, 
  ValidationResult, 
  ValidationError,
  CountdownData,
  TodayAnswerPreview,
  RecentAnswerPreview,
  PinpointSEOData
} from "@/types/pinpoint";

// URL slug生成函数
export function generateUrlSlug(gameNumber: number, clueWords: string[]): string {
  // 清理和格式化线索词
  const cleanWords = clueWords.map(word => 
    word.toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/-+/g, '-') // 多个连字符合并为一个
      .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
  ).filter(word => word.length > 0); // 过滤空字符串

  return `linkedin-pinpoint-${gameNumber}-${cleanWords.join('-')}`;
}

// 数据验证函数
export function validatePinpointData(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // 验证游戏编号
  if (!data.gameNumber || typeof data.gameNumber !== 'number' || data.gameNumber < 1) {
    errors.push({
      field: 'gameNumber',
      message: '游戏编号必须是正整数'
    });
  }

  // 验证日期
  if (!data.date || typeof data.date !== 'string') {
    errors.push({
      field: 'date',
      message: '日期不能为空'
    });
  } else {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.date)) {
      errors.push({
        field: 'date',
        message: '日期格式必须为 YYYY-MM-DD'
      });
    } else {
      const date = new Date(data.date);
      if (isNaN(date.getTime())) {
        errors.push({
          field: 'date',
          message: '日期格式无效'
        });
      }
    }
  }

  // 验证答案
  if (!data.answer || typeof data.answer !== 'string' || data.answer.trim().length === 0) {
    errors.push({
      field: 'answer',
      message: '答案不能为空'
    });
  } else if (data.answer.trim().length > 255) {
    errors.push({
      field: 'answer',
      message: '答案长度不能超过255个字符'
    });
  }

  // 验证线索词
  if (!data.clueWords || !Array.isArray(data.clueWords) || data.clueWords.length !== 5) {
    errors.push({
      field: 'clueWords',
      message: '必须提供5个线索词'
    });
  } else {
    data.clueWords.forEach((word: any, index: number) => {
      if (!word || typeof word !== 'string' || word.trim().length === 0) {
        errors.push({
          field: `clueWords.${index}`,
          message: `线索词 ${index + 1} 不能为空`
        });
      } else if (word.trim().length > 100) {
        errors.push({
          field: `clueWords.${index}`,
          message: `线索词 ${index + 1} 长度不能超过100个字符`
        });
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 获取下一个谜题发布倒计时
export function getNextReleaseCountdown() {
  const now = new Date();

  // 当前 UTC 时间
  const nowUTC = now.getTime();

  // UTC 08:00 = 北京时间 16:00
  const releaseTime = new Date();
  // 夏令时是 7，冬令时是 8
  releaseTime.setUTCHours(7, 0, 0, 0); // 固定 UTC+0 的 08:00

  // 如果当前已过今天 UTC 08:00，目标就是明天的
  if (nowUTC >= releaseTime.getTime()) {
    releaseTime.setUTCDate(releaseTime.getUTCDate() + 1);
  }

  const diff = releaseTime.getTime() - nowUTC;

  return {
    totalMs: diff,
    hours: Math.floor(diff / 1000 / 60 / 60),
    minutes: Math.floor((diff / 1000 / 60) % 60),
    seconds: Math.floor((diff / 1000) % 60)
  };
}

// 获取下一个谜题发布时间（保持向后兼容）
export function getNextPuzzleTime(): Date {
  const countdown = getNextReleaseCountdown();
  return new Date(Date.now() + countdown.totalMs);
}

// 计算倒计时数据
export function calculateCountdown(targetTime: Date): CountdownData {
  const now = new Date();
  const diff = targetTime.getTime() - now.getTime();
  
  if (diff <= 0) {
    return {
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalSeconds: 0,
      isExpired: true
    };
  }
  
  const totalSeconds = Math.floor(diff / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  return {
    hours,
    minutes,
    seconds,
    totalSeconds,
    isExpired: false
  };
}

// 格式化倒计时显示
export function formatCountdown(countdown: CountdownData): string {
  if (countdown.isExpired) {
    return "00:00:00";
  }
  
  return `${countdown.hours.toString().padStart(2, '0')}:${countdown.minutes.toString().padStart(2, '0')}:${countdown.seconds.toString().padStart(2, '0')}`;
}

// 数据库格式转换为前端格式
export function formatPinpointAnswer(answer: PinpointAnswer): PinpointAnswerFormatted {
  return {
    gameNumber: answer.game_number,
    date: answer.date,
    answer: answer.answer,
    clues: [
      answer.clue_word_1,
      answer.clue_word_2,
      answer.clue_word_3,
      answer.clue_word_4,
      answer.clue_word_5,
    ],
    urlSlug: answer.url_slug,
    createdAt: answer.created_at,
    updatedAt: answer.updated_at,
    status: answer.status,
  };
}

// 前端格式转换为数据库格式
export function formatToDatabase(data: PinpointFormData): Omit<PinpointAnswer, 'id' | 'created_at' | 'updated_at'> {
  const urlSlug = generateUrlSlug(data.gameNumber, data.clueWords);

  return {
    game_number: data.gameNumber,
    date: data.date,
    answer: data.answer.trim(),
    clue_word_1: data.clueWords[0].trim(),
    clue_word_2: data.clueWords[1].trim(),
    clue_word_3: data.clueWords[2].trim(),
    clue_word_4: data.clueWords[3].trim(),
    clue_word_5: data.clueWords[4].trim(),
    url_slug: urlSlug,
    status: data.status || 'published',
  };
}

// 生成今日答案预览数据
export function generateTodayPreview(answer?: PinpointAnswer): TodayAnswerPreview {
  if (!answer) {
    return {
      gameNumber: 0,
      date: new Date().toISOString().split('T')[0],
      hasAnswer: false,
    };
  }

  return {
    gameNumber: answer.game_number,
    date: answer.date,
    hasAnswer: true,
    urlSlug: answer.url_slug,
    answerPreview: `${answer.answer.substring(0, 20)}${answer.answer.length > 20 ? '...' : ''}`,
  };
}

// 生成最近答案预览列表
export function generateRecentPreviews(answers: PinpointAnswer[]): RecentAnswerPreview[] {
  return answers.map(answer => ({
    gameNumber: answer.game_number,
    date: answer.date,
    answer: answer.answer,
    urlSlug: answer.url_slug,
  }));
}

// 生成SEO数据
export function generateSEOData(answer: PinpointAnswerFormatted, baseUrl: string, locale: string = "zh"): PinpointSEOData {
  // 使用线索词作为主要title，更符合用户搜索习惯
  const cluesText = answer.clues.join(' ');

  let title: string;
  let description: string;
  let keywords: string[];
  let siteName: string;
  let ogDescription: string;

  if (locale === "zh") {
    title = `Pinpoint #${answer.gameNumber}: ${cluesText} - 答案解析`;
    description = `LinkedIn Pinpoint 第${answer.gameNumber}期答案揭晓！线索词"${answer.clues.join('、')}"的答案是"${answer.answer}"。查看详细解析和解题思路。`;
    siteName = "LinkedIn Pinpoint 每日答案";
    ogDescription = `线索词：${answer.clues.join('、')} → 点击查看答案`;
    keywords = [
      'LinkedIn Pinpoint',
      'Pinpoint答案',
      `Pinpoint ${answer.gameNumber}`,
      cluesText, // 线索词组合
      `${cluesText} 答案`, // 线索词+答案组合
      answer.answer,
      ...answer.clues,
      '每日答案',
      '游戏攻略',
      '解题思路'
    ];
  } else {
    title = `LinkedIn Pinpoint Today #${answer.gameNumber}: ${cluesText} - Answer Analysis`;
    description = `LinkedIn Pinpoint Today #${answer.gameNumber} answer revealed! The clues "${answer.clues.join(', ')}" lead to the answer "${answer.answer}".`;
    siteName = "LinkedIn Pinpoint Today Answers";
    ogDescription = `Clues: ${answer.clues.join(', ')} → Click to reveal answer`;
    keywords = [
      `LinkedIn Pinpoint Today ${answer.gameNumber}`,
      cluesText, // clue words combination
      `${cluesText} answer`, // clues+answer combination
      answer.answer,
    ];
  }

  const canonicalUrl = `${baseUrl}/${answer.urlSlug}`;

  return {
    title: `${title} | ${siteName}`,
    description,
    keywords,
    canonicalUrl,
    openGraph: {
      title,
      description: ogDescription,
      type: 'article',
      publishedTime: answer.createdAt,
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary',
      title,
      description: `线索词：${answer.clues.join('、')} - 点击查看答案`,
    },
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: title,
      description,
      author: {
        '@type': 'Organization',
        name: 'Pinpoint Daily Answers',
      },
      publisher: {
        '@type': 'Organization',
        name: 'Pinpoint Daily Answers',
      },
      datePublished: answer.createdAt,
      dateModified: answer.updatedAt,
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': canonicalUrl,
      },
    },
  };
}

// 日期格式化工具
export function formatDate(dateString: string, locale: string = 'zh-CN'): string {
  const date = new Date(dateString);
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// 相对时间格式化
export function formatRelativeTime(dateString: string, locale: string = 'zh-CN'): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return '今天';
  if (diffInDays === 1) return '昨天';
  if (diffInDays === 2) return '前天';
  if (diffInDays < 7) return `${diffInDays}天前`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)}周前`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)}个月前`;
  
  return `${Math.floor(diffInDays / 365)}年前`;
}

// 检查是否为今天
export function isToday(dateString: string): boolean {
  const date = new Date(dateString);
  const today = new Date();
  
  return date.getFullYear() === today.getFullYear() &&
         date.getMonth() === today.getMonth() &&
         date.getDate() === today.getDate();
}

// 生成分页信息
export function generatePagination(page: number, totalPages: number, maxVisible: number = 5) {
  const pages: (number | string)[] = [];
  
  if (totalPages <= maxVisible) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    const start = Math.max(1, page - Math.floor(maxVisible / 2));
    const end = Math.min(totalPages, start + maxVisible - 1);
    
    if (start > 1) {
      pages.push(1);
      if (start > 2) pages.push('...');
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    if (end < totalPages) {
      if (end < totalPages - 1) pages.push('...');
      pages.push(totalPages);
    }
  }
  
  return pages;
}
