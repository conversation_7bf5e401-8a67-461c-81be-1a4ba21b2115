{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "pinpoint": {"today_pinpoint": "今日 Pinpoint", "no_answer_today": "今日暂无答案", "no_answer_description": "今日的Pinpoint答案尚未发布", "next_puzzle_countdown": "下一个谜题 LinkedIn Pinpoint #{nextNumber} 倒计时", "next_puzzle_countdown_short": "下一个谜题 LinkedIn Pinpoint #{nextNumber} 倒计时", "clues": "线索词", "clue_number": "线索 {number}", "today_answer": "今日答案", "reveal_answer": "点击揭晓答案", "hide_answer": "隐藏答案", "view_details": "查看详情", "game_number": "#{number}", "recent_answers": "最近答案", "view_all_history": "查看所有历史答案", "how_to_play": "游戏说明", "answer_history": "答案历史", "search_placeholder": "搜索答案...", "no_results": "未找到结果", "loading": "加载中...", "date_format": "{month}月{day}日 {weekday}", "latest": "最新", "guess_answer": "🤔 猜猜答案是什么？", "clues_label": "线索词:", "recent_answers_description": "查看最近发布的Pinpoint答案"}, "homepage": {"title": "LinkedIn Pinpoint 每日答案", "subtitle": "每日答案", "description": "获取最新的Pinpoint游戏答案、线索词解析和历史答案查询。", "description_line2": "每日更新，助您轻松通关文字游戏。", "nav_history": "历史答案", "nav_rules": "游戏规则", "nav_play": "开始游戏", "about_title": "关于 LinkedIn Pinpoint", "game_rules_title": "游戏规则", "how_to_use_title": "如何使用", "rule_1": "每天发布一个新的谜题", "rule_2": "根据5个线索词找出共同点", "rule_3": "答案通常是一个类别、主题或概念", "rule_4": "每个谜题都有唯一的编号", "usage_1": "查看今日答案和线索词", "usage_2": "浏览历史答案和解析", "usage_3": "分享答案给朋友", "usage_4": "每日更新，不错过任何答案", "error_title": "LinkedIn Pinpoint 每日答案", "error_message": "抱歉，暂时无法加载数据，请稍后再试。", "error_button": "查看历史答案", "meta_title": "LinkedIn Pinpoint 每日答案 - 获取最新游戏答案和解析", "meta_description": "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。每日更新，助您轻松通关Pinpoint文字游戏。"}, "history": {"title": "历史答案", "description": "浏览所有LinkedIn Pinpoint历史答案，快速找到您需要的游戏解答", "back_to_home": "返回首页", "search_placeholder": "搜索答案、线索词或游戏编号...", "search_button": "搜索", "total_answers": "共找到 {count} 个答案", "search_query": "搜索: \"{query}\"", "no_results": "未找到匹配的答案", "no_answers": "暂无历史答案", "try_other_keywords": "尝试使用其他关键词搜索", "will_show_here": "历史答案将在这里显示", "view_all_answers": "查看所有答案", "previous_page": "上一页", "next_page": "下一页", "error_loading": "加载历史答案失败，请稍后再试。", "meta_title": "历史答案 - LinkedIn Pinpoint 每日答案", "meta_description": "浏览LinkedIn Pinpoint所有历史答案，包括游戏编号、日期、答案和线索词。快速查找您需要的Pinpoint答案。"}, "answer_detail": {"back_to_home": "返回首页", "back_to_history": "返回历史", "share": "分享", "copy_link": "复制链接", "link_copied": "链接已复制", "answer_revealed": "答案揭晓", "clues": "线索词", "clue_number": "线索 {number}", "game_info": "游戏信息", "game_number": "游戏编号", "release_date": "发布日期", "next_puzzle": "下一个谜题", "countdown_label": "距离下一个谜题发布", "related_answers": "相关答案", "view_more": "查看更多", "not_found_title": "答案未找到", "not_found_description": "抱歉，您查找的Pinpoint答案不存在。", "error_loading": "加载答案详情失败，请稍后再试。", "about_pinpoint": "关于 LinkedIn Pinpoint", "game_rules_section": "游戏规则", "quick_navigation": "快速导航", "view_history": "查看历史答案", "back_home": "返回首页", "puzzle_release_info": "新的Pinpoint谜题将在太平洋时间午夜发布"}, "answer_reveal": {"thinking_hints": ["先想想这5个线索词的共同点...", "它们可能有什么关联呢？", "试着找找规律吧！", "这些词背后隐藏着什么秘密？", "仔细观察，答案就在眼前...", "每个词都有它的作用，找找共同之处吧！", "别着急，慢慢思考这些线索...", "答案比你想象的更简单！"], "ready_title": "准备好看答案了吗？", "reveal_button": "🎯 揭晓答案", "thinking_stage": "思考中...", "thinking_prompt": "让我们一起分析这些线索词", "direct_view": "直接查看答案", "answer_revealed": "🎉 答案揭晓！", "clue_preview": "线索：{clues}"}, "solving_strategy": {"title": "💡 解题思路", "expand": "展开", "collapse": "收起", "step1_title": "观察线索词", "step1_content": "分析这5个词：{clues}", "step1_detail": "注意它们的词性、长度、特征", "step2_title": "寻找共同点", "step2_content": "这些词都可以与某个概念关联", "step2_detail": "考虑它们在不同语境下的含义", "step3_title": "验证答案", "step3_content": "确认每个线索词都符合答案的逻辑", "step3_detail": "检查是否有遗漏或例外", "tips_title": "🎯 解题小贴士", "tips": ["先快速浏览所有线索词，寻找明显的共同特征", "考虑词汇的不同含义和用法", "想想这些词在日常生活中的常见搭配", "如果卡住了，试着从不同角度思考每个词", "有时答案就是最简单直接的那个"]}}