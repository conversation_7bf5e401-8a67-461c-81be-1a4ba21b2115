{"metadata": {"title": "Ship Any AI SaaS Startups in hours | ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.", "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "pinpoint": {"today_pinpoint": "Today's Pinpoint", "no_answer_today": "No Answer Today", "no_answer_description": "Today's Pinpoint answer has not been released yet", "next_puzzle_countdown": "Next Puzzle LinkedIn Pinpoint #{nextNumber} Countdown", "next_puzzle_countdown_short": "Next Puzzle LinkedIn Pinpoint #{nextNumber} Countdown", "clues": "Clues", "clue_number": "Clue {number}", "today_answer": "Today's Answer", "reveal_answer": "🔍 Click to Reveal Answer", "hide_answer": "Hide Answer", "view_details": "View Details", "game_number": "#{number}", "recent_answers": "Recent Answers", "view_all_history": "View All History", "how_to_play": "How to Play", "answer_history": "Answer History", "search_placeholder": "Search answers...", "no_results": "No results found", "loading": "Loading...", "date_format": "{weekday}, {month} {day}", "latest": "Latest", "guess_answer": "🤔 Guess the answer?", "clues_label": "Clues:", "recent_answers_description": "View recently released Pinpoint answers"}, "homepage": {"title": "LinkedIn Pinpoint Daily Answers", "subtitle": "Daily Answers", "description": "Get the latest Pinpoint game answers, clue analysis, and historical answer queries.", "description_line2": "Updated daily to help you easily master the word game.", "nav_history": "Answer History", "nav_rules": "Game Rules", "nav_play": "Start Playing", "about_title": "About LinkedIn Pinpoint", "game_rules_title": "Game Rules", "how_to_use_title": "How to Use", "rule_1": "A new puzzle is released every day", "rule_2": "Find the common thread among 5 clue words", "rule_3": "The answer is usually a category, theme, or concept", "rule_4": "Each puzzle has a unique number", "usage_1": "View today's answer and clue words", "usage_2": "Browse historical answers and analysis", "usage_3": "Share answers with friends", "usage_4": "Daily updates, never miss any answer", "error_title": "LinkedIn Pinpoint Daily Answers", "error_message": "Sorry, unable to load data at the moment. Please try again later.", "error_button": "View Answer History", "meta_title": "LinkedIn Pinpoint Daily Answers - Get Latest Game Answers and Analysis", "meta_description": "Get LinkedIn Pinpoint daily game answers, clue word analysis, and historical answer queries. Updated daily to help you easily master the Pinpoint word game."}, "history": {"title": "Answer History", "description": "Browse all LinkedIn Pinpoint historical answers and quickly find the game solutions you need", "back_to_home": "Back to Home", "search_placeholder": "Search answers, clues, or game numbers...", "search_button": "Search", "total_answers": "Found {count} answers", "search_query": "Search: \"{query}\"", "no_results": "No matching answers found", "no_answers": "No historical answers yet", "try_other_keywords": "Try searching with other keywords", "will_show_here": "Historical answers will be displayed here", "view_all_answers": "View All Answers", "previous_page": "Previous", "next_page": "Next", "error_loading": "Failed to load answer history. Please try again later.", "meta_title": "Answer History - LinkedIn Pinpoint Daily Answers", "meta_description": "Browse all LinkedIn Pinpoint historical answers, including game numbers, dates, answers and clue words. Quickly find the Pinpoint answers you need.", "view_history_answers": "📚 View History & Answers", "recent_answers": "Recent Answers", "recent_answers_description": "Browse through the latest Pinpoint puzzles and their solutions", "view_all_history": "View All History", "no_recent_answers": "No recent answers available", "loading_answers": "Loading answers..."}, "answer_detail": {"back_to_home": "Back to Home", "back_to_history": "Back to History", "share": "Share", "copy_link": "Copy Link", "link_copied": "<PERSON>d", "answer_revealed": "Answer Revealed", "clues": "Clues", "clue_number": "Clue {number}", "game_info": "Game Info", "game_number": "Game Number", "release_date": "Release Date", "next_puzzle": "Next Puzzle", "countdown_label": "Until Next Puzzle Release", "related_answers": "Related Answers", "view_more": "View More", "not_found_title": "Answer Not Found", "not_found_description": "Sorry, the Pinpoint answer you're looking for doesn't exist.", "error_loading": "Failed to load answer details. Please try again later.", "about_pinpoint": "About LinkedIn Pinpoint", "game_rules_section": "Game Rules", "quick_navigation": "Quick Navigation", "view_history": "View Answer History", "back_home": "Back to Home", "puzzle_release_info": "New Pinpoint puzzle will be released at midnight Pacific Time"}, "answer_reveal": {"thinking_hints": ["Think about what these 5 clue words have in common...", "What connection might they share?", "Try to find the pattern!", "What secret do these words hide?", "Look carefully, the answer is right there...", "Each word has its purpose, find the common thread!", "Take your time to think about these clues...", "The answer is simpler than you think!"], "ready_title": "Ready to see the answer?", "reveal_button": "🎯 Reveal Answer", "thinking_stage": "Thinking...", "thinking_prompt": "Let's analyze these clue words together", "direct_view": "View answer directly", "answer_revealed": "🎉 Answer Revealed!", "clue_preview": "Clues: {clues}"}, "solving_strategy": {"title": "💡 Solving Strategy", "expand": "Expand", "collapse": "Collapse", "show_strategy": "💡 View Solving Strategy", "hide_strategy": "💡 Hide Solving Strategy", "step1_title": "Observe the clues", "step1_content": "Analyze these 5 words: {clues}", "step1_detail": "Notice their parts of speech, length, and characteristics", "step2_title": "Find common ground", "step2_content": "These words can all be associated with a certain concept", "step2_detail": "Consider their meanings in different contexts", "step3_title": "Verify the answer", "step3_content": "Confirm that each clue word fits the answer's logic", "step3_detail": "Check for any omissions or exceptions", "tips_title": "🎯 Solving Tips", "tips": ["Quickly scan all clue words first, looking for obvious common features", "Consider different meanings and uses of the words", "Think about common word combinations in daily life", "If stuck, try thinking about each word from different angles", "Sometimes the answer is the most straightforward one"]}, "challenge": {"start_challenge": "🎮 Start Challenge", "challenge_description": "Test your skills with random Pinpoint puzzles!", "loading": "Loading questions...", "challenge_complete": "Challenge Complete!", "final_score": "Final Score", "max_streak": "<PERSON>", "time_taken": "Time Taken", "play_again": "Play Again", "back": "Back", "question_progress": "Question {current}/{total}", "score": "Score", "streak": "Streak", "todays_clues": "What are today's clue words?", "your_answer": "What's your answer?", "enter_answer": "Enter your answer...", "submit_answer": "Submit Answer", "correct": "Correct!", "incorrect": "Incorrect!", "correct_answer": "Correct Answer:", "next_question": "Next Question", "view_results": "View Results", "streak_good": "Good streak!", "streak_fire": "On fire!", "streak_unstoppable": "Unstoppable!"}}