import Link from "next/link";
import { useTranslations } from "next-intl";
import { ChevronRight, Home } from "lucide-react";
import { PinpointAnswerFormatted } from "@/types/pinpoint";

interface BreadcrumbNavigationProps {
  answer: PinpointAnswerFormatted;
  locale: string;
}

export default function BreadcrumbNavigation({ answer, locale }: BreadcrumbNavigationProps) {
  const t = useTranslations("breadcrumb");

  const breadcrumbItems = [
    {
      label: t("home"),
      href: locale === "en" ? "/" : `/${locale}`,
      icon: <Home className="w-4 h-4" />
    },
    {
      label: t("answers"),
      href: locale === "en" ? "/history" : `/${locale}/history`,
    },
    {
      label: `#${answer.gameNumber}`,
      href: null, // 当前页面
    }
  ];

  return (
    <nav className="mb-6" aria-label="Breadcrumb">
      <div className="flex items-center space-x-2 text-sm">
        {breadcrumbItems.map((item, index) => (
          <div key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 mx-2" style={{ color: 'var(--color-text-tertiary)' }} />
            )}
            
            {item.href ? (
              <Link 
                href={item.href}
                className="flex items-center gap-1 hover:underline transition-colors"
                style={{ color: 'var(--color-text-secondary)' }}
              >
                {item.icon}
                <span>{item.label}</span>
              </Link>
            ) : (
              <span className="flex items-center gap-1 font-medium" style={{ color: 'var(--color-text-primary)' }}>
                {item.icon}
                <span>{item.label}</span>
              </span>
            )}
          </div>
        ))}
      </div>

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbItems.map((item, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": item.label,
              "item": item.href ? `${process.env.NEXT_PUBLIC_WEB_URL}${item.href}` : undefined
            }))
          })
        }}
      />
    </nav>
  );
}
