"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { PinpointAnswerFormatted } from "@/types/pinpoint";

interface StrategyStep {
  id: string;
  title: string;
  content: string;
  detail: string;
  icon: string;
}

export function useSolvingStrategy(answer: PinpointAnswerFormatted) {
  const [isExpanded, setIsExpanded] = useState(false);
  const t = useTranslations('solving_strategy');

  // 生成解题步骤内容
  const generateSteps = (): StrategyStep[] => {
    const cluesText = answer.clues.join('、');
    
    return [
      {
        id: 'step1',
        title: t('step1_title'),
        content: t('step1_content', { clues: cluesText }),
        detail: t('step1_detail'),
        icon: '🔍'
      },
      {
        id: 'step2',
        title: t('step2_title'),
        content: t('step2_content'),
        detail: t('step2_detail'),
        icon: '💡'
      },
      {
        id: 'step3',
        title: t('step3_title'),
        content: t('step3_content'),
        detail: t('step3_detail'),
        icon: '✅'
      }
    ];
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    
    // 用户行为追踪
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'solving_strategy_toggle', {
        expanded: !isExpanded,
        engagement_type: 'content_interaction'
      });
    }
  };

  const steps = generateSteps();
  const tips = t.raw('tips') as string[];

  return {
    isExpanded,
    toggleExpanded,
    steps,
    tips,
    t
  };
}
