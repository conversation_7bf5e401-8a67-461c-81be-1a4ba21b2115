"use client";

interface TipsSectionProps {
  tips: string[];
  tipsTitle: string;
}

export default function TipsSection({ tips, tipsTitle }: TipsSectionProps) {
  return (
    <div className="mt-8 p-6 bg-blue-50 rounded-xl">
      <h4 className="font-semibold mb-3 flex items-center gap-2">
        {tipsTitle}
      </h4>
      <ul className="space-y-2 text-sm text-gray-700">
        {tips.map((tip, index) => (
          <li key={index} className="flex items-start gap-3">
            <span 
              className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
              style={{ backgroundColor: 'var(--color-primary)' }}
            ></span>
            <span>{tip}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
