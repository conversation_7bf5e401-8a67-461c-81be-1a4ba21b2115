"use client";

interface StrategyStepProps {
  step: {
    id: string;
    title: string;
    content: string;
    detail: string;
    icon: string;
  };
  index: number;
}

export default function StrategyStep({ step, index }: StrategyStepProps) {
  return (
    <div 
      className="flex items-start gap-4 p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors animate-fade-in"
      style={{ animationDelay: `${index * 150}ms` }}
    >
      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white flex items-center justify-center font-bold text-lg flex-shrink-0">
        {index + 1}
      </div>
      <div className="flex-1">
        <h4 className="font-semibold mb-2 flex items-center gap-2">
          <span>{step.icon}</span>
          {step.title}
        </h4>
        <p className="text-gray-700 mb-2">{step.content}</p>
        <p className="text-sm text-gray-600">{step.detail}</p>
      </div>
    </div>
  );
}
