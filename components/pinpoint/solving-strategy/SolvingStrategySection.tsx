"use client";

import { ChevronDown, ChevronUp } from "lucide-react";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { useSolvingStrategy } from "./useSolvingStrategy";
import StrategyStep from "./StrategyStep";
import TipsSection from "./TipsSection";

interface SolvingStrategySectionProps {
  answer: PinpointAnswerFormatted;
}

export default function SolvingStrategySection({ answer }: SolvingStrategySectionProps) {
  const { isExpanded, toggleExpanded, steps, tips, t } = useSolvingStrategy(answer);

  return (
    <div className="apple-card p-12 mb-12">
      {/* SEO内容层 - 始终在HTML中 */}
      <div className="strategy-content">
        <h3 className="apple-title flex items-center gap-3 mb-8">
          {t('title')}
        </h3>
        
        {/* 解题步骤 - SEO可见 */}
        <div className="steps space-y-6">
          {steps.map((step, index) => (
            <div key={step.id} className="step">
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <span>{step.icon}</span>
                {step.title}
              </h4>
              <p className="text-gray-700 mb-2">{step.content}</p>
              <p className="text-sm text-gray-600">{step.detail}</p>
            </div>
          ))}
        </div>

        {/* 解题小贴士 - SEO可见 */}
        <div className="tips mt-8">
          <h4 className="font-semibold mb-3">{t('tips_title')}</h4>
          <ul className="space-y-2 text-sm text-gray-700">
            {tips.map((tip, index) => (
              <li key={index}>{tip}</li>
            ))}
          </ul>
        </div>
      </div>

      {/* 交互控制层 */}
      <div className="interaction-layer">
        <div 
          className="flex items-center justify-between cursor-pointer mb-8"
          onClick={toggleExpanded}
        >
          <h3 className="apple-title flex items-center gap-3">
            {t('title')}
          </h3>
          <div className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700 transition-colors">
            <span>{isExpanded ? t('collapse') : t('expand')}</span>
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </div>
        </div>
        
        {/* 可展开的内容 */}
        <div 
          className={`expandable-content transition-all duration-300 overflow-hidden ${
            isExpanded ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="space-y-6 animate-fade-in">
            {steps.map((step, index) => (
              <StrategyStep key={step.id} step={step} index={index} />
            ))}
            
            <TipsSection tips={tips} tipsTitle={t('tips_title')} />
          </div>
        </div>
      </div>
    </div>
  );
}
