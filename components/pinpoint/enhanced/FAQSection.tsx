import { useState } from "react";
import { useTranslations } from "next-intl";
import { ChevronDown, ChevronUp, HelpCircle } from "lucide-react";
import { PinpointAnswerFormatted } from "@/types/pinpoint";

interface FAQSectionProps {
  answer: PinpointAnswerFormatted;
  locale: string;
}

export default function FAQSection({ answer, locale }: FAQSectionProps) {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const t = useTranslations("faq");

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // 生成动态FAQ内容
  const generateFAQs = () => {
    const faqs = [];

    // 基础FAQ
    faqs.push({
      question: locale === "zh" 
        ? `Pinpoint #${answer.gameNumber} 的答案是什么？`
        : `What is the answer to Pinpoint #${answer.gameNumber}?`,
      answer: locale === "zh"
        ? `Pinpoint #${answer.gameNumber} 的答案是"${answer.answer}"。这个答案通过分析线索词"${answer.clues.join('、')}"得出。`
        : `The answer to Pinpoint #${answer.gameNumber} is "${answer.answer}". This answer was derived by analyzing the clue words "${answer.clues.join(', ')}".`
    });

    // 解题策略FAQ
    faqs.push({
      question: locale === "zh"
        ? "如何解决这类Pinpoint谜题？"
        : "How do you solve this type of Pinpoint puzzle?",
      answer: locale === "zh"
        ? "解决Pinpoint谜题的关键是找到所有线索词之间的共同主题。首先分析每个词的多重含义，然后寻找它们的交集。建议从最明显的关联开始，逐步深入思考。"
        : "The key to solving Pinpoint puzzles is finding the common theme among all clue words. Start by analyzing multiple meanings of each word, then look for their intersection. It's recommended to start with the most obvious connections and gradually think deeper."
    });

    // 难度相关FAQ
    const difficultyLevel = answer.clues.length <= 3 ? "困难" : answer.clues.length >= 5 ? "简单" : "中等";
    const difficultyLevelEn = answer.clues.length <= 3 ? "hard" : answer.clues.length >= 5 ? "easy" : "medium";
    
    faqs.push({
      question: locale === "zh"
        ? `这道题的难度如何？`
        : `How difficult is this puzzle?`,
      answer: locale === "zh"
        ? `这道Pinpoint谜题的难度为${difficultyLevel}，共有${answer.clues.length}个线索词。难度评估基于线索词的数量和复杂程度。`
        : `This Pinpoint puzzle has a ${difficultyLevelEn} difficulty level with ${answer.clues.length} clue words. The difficulty assessment is based on the number and complexity of clue words.`
    });

    // 学习建议FAQ
    faqs.push({
      question: locale === "zh"
        ? "如何提高Pinpoint解题能力？"
        : "How can I improve my Pinpoint solving skills?",
      answer: locale === "zh"
        ? "提高Pinpoint解题能力需要多练习和扩展词汇量。建议每天练习，关注词汇的多重含义，培养联想思维。同时，学习不同领域的知识有助于理解更多概念之间的联系。"
        : "Improving Pinpoint solving skills requires practice and vocabulary expansion. It's recommended to practice daily, pay attention to multiple word meanings, and develop associative thinking. Learning knowledge from different fields also helps understand connections between various concepts."
    });

    // 相关游戏FAQ
    faqs.push({
      question: locale === "zh"
        ? "LinkedIn Pinpoint多久更新一次？"
        : "How often does LinkedIn Pinpoint update?",
      answer: locale === "zh"
        ? "LinkedIn Pinpoint每天都会发布新的谜题。通常在美国东部时间每天更新，为玩家提供新的挑战。建议关注我们的网站获取最新答案和解析。"
        : "LinkedIn Pinpoint releases new puzzles daily. Updates typically occur daily in Eastern Time, providing players with new challenges. We recommend following our website for the latest answers and analysis."
    });

    return faqs;
  };

  const faqs = generateFAQs();

  return (
    <div className="apple-card p-8">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 rounded-full flex items-center justify-center"
             style={{ backgroundColor: 'var(--color-primary)' }}>
          <HelpCircle className="w-5 h-5 text-white" />
        </div>
        <h3 className="apple-subtitle">{t("title")}</h3>
      </div>

      <div className="space-y-4">
        {faqs.map((faq, index) => (
          <div key={index} className="border rounded-lg overflow-hidden"
               style={{ borderColor: 'var(--color-border)' }}>
            <button
              onClick={() => toggleItem(index)}
              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              style={{ backgroundColor: openItems.includes(index) ? 'var(--color-surface-secondary)' : 'transparent' }}
            >
              <span className="font-medium apple-body">{faq.question}</span>
              {openItems.includes(index) ? (
                <ChevronUp className="w-5 h-5 flex-shrink-0" style={{ color: 'var(--color-text-secondary)' }} />
              ) : (
                <ChevronDown className="w-5 h-5 flex-shrink-0" style={{ color: 'var(--color-text-secondary)' }} />
              )}
            </button>
            
            {openItems.includes(index) && (
              <div className="px-6 py-4 border-t" style={{ borderColor: 'var(--color-border)' }}>
                <p className="apple-body leading-relaxed" style={{ color: 'var(--color-text-secondary)' }}>
                  {faq.answer}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqs.map(faq => ({
              "@type": "Question",
              "name": faq.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer
              }
            }))
          })
        }}
      />
    </div>
  );
}
