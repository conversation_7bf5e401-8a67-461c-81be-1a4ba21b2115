import { useTranslations } from "next-intl";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { Lightbulb, Target, BookOpen, TrendingUp } from "lucide-react";

interface AnswerAnalysisSectionProps {
  answer: PinpointAnswerFormatted;
  locale: string;
}

export default function AnswerAnalysisSection({ answer, locale }: AnswerAnalysisSectionProps) {
  const t = useTranslations("answer_analysis");

  // 生成解题策略内容
  const generateStrategy = () => {
    const strategies = [];
    
    // 基于线索词生成策略
    if (answer.clues.length > 0) {
      strategies.push({
        title: locale === "zh" ? "线索词分析" : "Clue Word Analysis",
        content: locale === "zh" 
          ? `本期的线索词包括"${answer.clues.join('、')}"。这些词汇之间的共同点指向了答案"${answer.answer}"。通过分析每个线索词的含义和它们之间的关联性，我们可以更好地理解解题思路。`
          : `This puzzle's clue words include "${answer.clues.join(', ')}". The common thread between these words points to the answer "${answer.answer}". By analyzing the meaning of each clue word and their relationships, we can better understand the solving approach.`
      });
    }

    // 添加解题技巧
    strategies.push({
      title: locale === "zh" ? "解题技巧" : "Solving Tips",
      content: locale === "zh"
        ? "在解决Pinpoint谜题时，关键是找到所有线索词之间的共同主题或概念。建议先分析每个词的多重含义，然后寻找它们的交集。有时答案可能是一个更抽象的概念，需要跳出字面意思思考。"
        : "When solving Pinpoint puzzles, the key is to find the common theme or concept among all clue words. Start by analyzing multiple meanings of each word, then look for their intersection. Sometimes the answer might be a more abstract concept that requires thinking beyond literal meanings."
    });

    // 添加相关概念
    strategies.push({
      title: locale === "zh" ? "相关概念" : "Related Concepts",
      content: locale === "zh"
        ? `答案"${answer.answer}"涉及多个相关概念和应用场景。理解这些关联可以帮助你在未来遇到类似线索时更快找到答案。建议扩展思维，考虑词汇在不同语境下的含义。`
        : `The answer "${answer.answer}" involves multiple related concepts and application scenarios. Understanding these connections can help you find answers faster when encountering similar clues in the future. It's recommended to expand your thinking and consider word meanings in different contexts.`
    });

    return strategies;
  };

  const strategies = generateStrategy();

  // 生成难度分析
  const getDifficultyAnalysis = () => {
    const clueCount = answer.clues.length;
    let difficulty = "medium";
    let difficultyText = "";

    if (clueCount <= 3) {
      difficulty = "hard";
      difficultyText = locale === "zh" ? "困难" : "Hard";
    } else if (clueCount >= 5) {
      difficulty = "easy";
      difficultyText = locale === "zh" ? "简单" : "Easy";
    } else {
      difficulty = "medium";
      difficultyText = locale === "zh" ? "中等" : "Medium";
    }

    return { difficulty, difficultyText };
  };

  const { difficulty, difficultyText } = getDifficultyAnalysis();

  return (
    <div className="space-y-8">
      {/* 难度分析 */}
      <div className="apple-card p-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-primary)' }}>
            <Target className="w-5 h-5 text-white" />
          </div>
          <h3 className="apple-subtitle">{t("difficulty_analysis")}</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="text-center p-4 rounded-lg" style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <div className="text-2xl font-bold mb-2" style={{ color: 'var(--color-primary)' }}>
              {difficultyText}
            </div>
            <div className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
              {t("difficulty_level")}
            </div>
          </div>
          
          <div className="text-center p-4 rounded-lg" style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <div className="text-2xl font-bold mb-2" style={{ color: 'var(--color-primary)' }}>
              {answer.clues.length}
            </div>
            <div className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
              {t("clue_count")}
            </div>
          </div>
          
          <div className="text-center p-4 rounded-lg" style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <div className="text-2xl font-bold mb-2" style={{ color: 'var(--color-primary)' }}>
              #{answer.gameNumber}
            </div>
            <div className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
              {t("puzzle_number")}
            </div>
          </div>
        </div>

        <p className="apple-body">
          {locale === "zh" 
            ? `这道Pinpoint谜题的难度为${difficultyText}，共有${answer.clues.length}个线索词。基于线索词的数量和复杂程度，我们评估了这道题的解题难度。`
            : `This Pinpoint puzzle has a ${difficultyText.toLowerCase()} difficulty level with ${answer.clues.length} clue words. We assessed the solving difficulty based on the number and complexity of the clue words.`
          }
        </p>
      </div>

      {/* 解题策略 */}
      <div className="apple-card p-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-primary)' }}>
            <Lightbulb className="w-5 h-5 text-white" />
          </div>
          <h3 className="apple-subtitle">{t("solving_strategy")}</h3>
        </div>

        <div className="space-y-6">
          {strategies.map((strategy, index) => (
            <div key={index} className="border-l-4 pl-6" style={{ borderColor: 'var(--color-primary)' }}>
              <h4 className="font-semibold mb-3 apple-body-large">{strategy.title}</h4>
              <p className="apple-body leading-relaxed">{strategy.content}</p>
            </div>
          ))}
        </div>
      </div>

      {/* 学习要点 */}
      <div className="apple-card p-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-primary)' }}>
            <BookOpen className="w-5 h-5 text-white" />
          </div>
          <h3 className="apple-subtitle">{t("key_learnings")}</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold mb-3 apple-body-large">{t("vocabulary_expansion")}</h4>
            <ul className="space-y-2">
              {answer.clues.map((clue, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                        style={{ backgroundColor: 'var(--color-primary)' }}></span>
                  <span className="apple-body">
                    <strong>{clue}</strong> - {locale === "zh" ? "扩展相关词汇和概念" : "Expand related vocabulary and concepts"}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-3 apple-body-large">{t("thinking_patterns")}</h4>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                      style={{ backgroundColor: 'var(--color-primary)' }}></span>
                <span className="apple-body">
                  {locale === "zh" ? "联想思维：从线索词联想到相关概念" : "Associative thinking: Connect clue words to related concepts"}
                </span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                      style={{ backgroundColor: 'var(--color-primary)' }}></span>
                <span className="apple-body">
                  {locale === "zh" ? "逻辑推理：分析词汇间的逻辑关系" : "Logical reasoning: Analyze logical relationships between words"}
                </span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                      style={{ backgroundColor: 'var(--color-primary)' }}></span>
                <span className="apple-body">
                  {locale === "zh" ? "创新思考：跳出常规思维模式" : "Creative thinking: Think outside conventional patterns"}
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
