import { useTranslations } from "next-intl";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { ArrowRight, Hash, Calendar } from "lucide-react";

interface RelatedAnswersSectionProps {
  currentAnswer: PinpointAnswerFormatted;
  allAnswers: PinpointAnswerFormatted[];
  locale: string;
}

export default function RelatedAnswersSection({ 
  currentAnswer, 
  allAnswers, 
  locale 
}: RelatedAnswersSectionProps) {
  const t = useTranslations("answer_analysis");

  // 找到相关答案的算法
  const findRelatedAnswers = () => {
    const related = [];
    
    // 1. 找到包含相同线索词的答案
    const sameClueAnswers = allAnswers.filter(answer => 
      answer.gameNumber !== currentAnswer.gameNumber &&
      answer.clues.some(clue => currentAnswer.clues.includes(clue))
    ).slice(0, 2);
    
    if (sameClueAnswers.length > 0) {
      related.push({
        title: locale === "zh" ? "包含相似线索词" : "Similar Clue Words",
        answers: sameClueAnswers,
        description: locale === "zh" 
          ? "这些谜题包含了相同或相似的线索词"
          : "These puzzles contain the same or similar clue words"
      });
    }

    // 2. 找到相似难度的答案（基于线索词数量）
    const similarDifficulty = allAnswers.filter(answer => 
      answer.gameNumber !== currentAnswer.gameNumber &&
      Math.abs(answer.clues.length - currentAnswer.clues.length) <= 1 &&
      !sameClueAnswers.includes(answer)
    ).slice(0, 3);

    if (similarDifficulty.length > 0) {
      related.push({
        title: locale === "zh" ? "相似难度" : "Similar Difficulty",
        answers: similarDifficulty,
        description: locale === "zh" 
          ? "这些谜题具有相似的难度等级"
          : "These puzzles have similar difficulty levels"
      });
    }

    // 3. 找到最近的答案
    const recentAnswers = allAnswers
      .filter(answer => answer.gameNumber !== currentAnswer.gameNumber)
      .sort((a, b) => b.gameNumber - a.gameNumber)
      .slice(0, 3);

    if (recentAnswers.length > 0 && related.length < 2) {
      related.push({
        title: locale === "zh" ? "最近答案" : "Recent Answers",
        answers: recentAnswers,
        description: locale === "zh" 
          ? "最新发布的Pinpoint谜题答案"
          : "Recently published Pinpoint puzzle answers"
      });
    }

    return related.slice(0, 2); // 最多显示2个分类
  };

  const relatedGroups = findRelatedAnswers();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";
    return date.toLocaleDateString(localeCode, {
      month: "short",
      day: "numeric",
    });
  };

  const getAnswerUrl = (answer: PinpointAnswerFormatted) => {
    if (locale === 'en') {
      return `/${answer.urlSlug}`;
    }
    return `/${locale}/${answer.urlSlug}`;
  };

  if (relatedGroups.length === 0) {
    return null;
  }

  return (
    <div className="apple-card p-8">
      <h3 className="apple-subtitle mb-6">{t("related_puzzles")}</h3>
      
      <div className="space-y-8">
        {relatedGroups.map((group, groupIndex) => (
          <div key={groupIndex}>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                   style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                {groupIndex + 1}
              </div>
              <div>
                <h4 className="font-semibold apple-body-large">{group.title}</h4>
                <p className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                  {group.description}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {group.answers.map((answer) => (
                <Link
                  key={answer.gameNumber}
                  href={getAnswerUrl(answer)}
                  className="block group"
                >
                  <div className="p-4 rounded-lg border transition-all duration-300 hover:shadow-md"
                       style={{ 
                         borderColor: 'var(--color-border)',
                         backgroundColor: 'var(--color-surface-secondary)'
                       }}>
                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center"
                             style={{ backgroundColor: 'var(--color-primary)' }}>
                          <Hash className="w-3 h-3 text-white" />
                        </div>
                        <span className="font-semibold text-sm" style={{ color: 'var(--color-primary)' }}>
                          #{answer.gameNumber}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(answer.date)}</span>
                      </div>
                    </div>

                    {/* Clues */}
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-1">
                        {answer.clues.slice(0, 3).map((clue, clueIndex) => (
                          <span
                            key={clueIndex}
                            className="inline-block px-2 py-1 rounded text-xs font-medium"
                            style={{
                              backgroundColor: 'var(--color-primary)',
                              color: 'white'
                            }}
                          >
                            {clue}
                          </span>
                        ))}
                        {answer.clues.length > 3 && (
                          <span className="inline-block px-2 py-1 rounded text-xs"
                                style={{ color: 'var(--color-text-secondary)' }}>
                            +{answer.clues.length - 3}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Arrow */}
                    <div className="flex justify-end">
                      <div className="transition-transform duration-300 group-hover:translate-x-1">
                        <ArrowRight className="w-4 h-4" style={{ color: 'var(--color-text-tertiary)' }} />
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 查看更多链接 */}
      <div className="mt-6 text-center">
        <Link 
          href={locale === "en" ? "/history" : `/${locale}/history`}
          className="apple-button apple-button-secondary"
        >
          {t("practice_more")}
        </Link>
      </div>
    </div>
  );
}
