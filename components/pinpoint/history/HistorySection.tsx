"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Calendar, ArrowRight, Clock } from "lucide-react";
import { PinpointAnswerFormatted } from "@/types/pinpoint";

interface HistorySectionProps {
  currentAnswer?: PinpointAnswerFormatted;
}

export default function HistorySection({ currentAnswer }: HistorySectionProps) {
  const [recentAnswers, setRecentAnswers] = useState<PinpointAnswerFormatted[]>([]);
  const [loading, setLoading] = useState(true);
  const t = useTranslations('history');
  const params = useParams();
  const locale = params.locale as string;

  useEffect(() => {
    // 模拟获取最近的答案数据
    // 在实际应用中，这里应该调用API获取数据
    const fetchRecentAnswers = async () => {
      try {
        setLoading(true);
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 模拟数据 - 实际应该从API获取
        const mockAnswers: PinpointAnswerFormatted[] = [
          {
            id: "454",
            gameNumber: 454,
            date: "2025-01-07",
            answer: "Types of pasta",
            clues: ["penne", "fusilli", "rigatoni", "farfalle", "linguine"],
            urlSlug: "linkedin-pinpoint-454-penne-fusilli-rigatoni-farfalle-linguine",
            status: "published"
          },
          {
            id: "453",
            gameNumber: 453,
            date: "2025-01-06",
            answer: "Things you can break",
            clues: ["record", "news", "silence", "promise", "habit"],
            urlSlug: "linkedin-pinpoint-453-record-news-silence-promise-habit",
            status: "published"
          },
          {
            id: "452",
            gameNumber: 452,
            date: "2025-01-05",
            answer: "Words after 'time'",
            clues: ["zone", "limit", "machine", "capsule", "travel"],
            urlSlug: "linkedin-pinpoint-452-zone-limit-machine-capsule-travel",
            status: "published"
          }
        ];

        // 过滤掉当前答案（如果存在）
        const filteredAnswers = currentAnswer 
          ? mockAnswers.filter(answer => answer.gameNumber !== currentAnswer.gameNumber)
          : mockAnswers;

        setRecentAnswers(filteredAnswers.slice(0, 3)); // 只显示3个最近的答案
      } catch (error) {
        console.error('Failed to fetch recent answers:', error);
        setRecentAnswers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentAnswers();
  }, [currentAnswer]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    return date.toLocaleDateString(localeCode, {
      month: "short",
      day: "numeric",
    });
  };

  const getAnswerUrl = (answer: PinpointAnswerFormatted) => {
    if (locale === 'en') {
      return `/${answer.urlSlug}`;
    }
    return `/${locale}/${answer.urlSlug}`;
  };

  const getHistoryUrl = () => {
    if (locale === 'en') {
      return '/history';
    }
    return `/${locale}/history`;
  };

  if (loading) {
    return (
      <div className="apple-card p-8 mb-8">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded flex-1"></div>
                <div className="h-4 bg-gray-200 rounded w-8"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="apple-card p-8 mb-8">
      {/* Header */}
      <div className="mb-6">
        <h2 className="apple-subtitle mb-2 flex items-center gap-3">
          <Calendar className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
          {t('recent_answers')}
        </h2>
        <p className="apple-body text-sm">{t('recent_answers_description')}</p>
      </div>

      {/* Recent Answers List */}
      {recentAnswers.length > 0 ? (
        <div className="space-y-4 mb-6">
          {recentAnswers.map((answer) => (
            <Link
              key={answer.id}
              href={getAnswerUrl(answer)}
              className="block group"
            >
              <div 
                className="p-4 rounded-xl border transition-all duration-300 hover:shadow-md hover:-translate-y-1"
                style={{
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
                  borderColor: 'rgba(102, 126, 234, 0.1)',
                  borderWidth: '1px'
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span 
                        className="text-sm font-semibold px-2 py-1 rounded-full"
                        style={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white'
                        }}
                      >
                        #{answer.gameNumber}
                      </span>
                      <span className="text-sm text-gray-500 flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatDate(answer.date)}
                      </span>
                    </div>
                    <h3 className="font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                      {answer.answer}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {answer.clues.slice(0, 3).map((clue, index) => (
                        <span
                          key={index}
                          className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600"
                        >
                          {clue}
                        </span>
                      ))}
                      {answer.clues.length > 3 && (
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                          +{answer.clues.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                  <ArrowRight 
                    className="w-5 h-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all" 
                  />
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">📚</div>
          <p className="text-gray-500 mb-4">{t('no_recent_answers')}</p>
        </div>
      )}

      {/* View All History Button */}
      <div className="text-center">
        <Link
          href={getHistoryUrl()}
          className="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:-translate-y-1"
          style={{
            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            color: 'white',
            boxShadow: '0 6px 15px rgba(67, 233, 123, 0.3)'
          }}
        >
          <Calendar className="w-4 h-4" />
          {t('view_all_history')}
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
}
