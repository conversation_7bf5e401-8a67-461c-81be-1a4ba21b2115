"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Calendar, ArrowRight, Hash } from "lucide-react";
import { PinpointAnswerFormatted } from "@/types/pinpoint";

interface HistorySectionProps {
  currentAnswer?: PinpointAnswerFormatted;
}

export default function HistorySection({ currentAnswer }: HistorySectionProps) {
  const [recentAnswers, setRecentAnswers] = useState<PinpointAnswerFormatted[]>([]);
  const [loading, setLoading] = useState(true);
  const t = useTranslations('history');
  const tPinpoint = useTranslations('pinpoint');
  const params = useParams();
  const locale = params.locale as string;

  useEffect(() => {
    const fetchRecentAnswers = async () => {
      try {
        setLoading(true);

        // 构建API URL，如果有当前答案则排除它
        const excludeParam = currentAnswer ? `&exclude=${currentAnswer.gameNumber}` : '';
        const apiUrl = `/api/pinpoint/recent?limit=3${excludeParam}`;

        // 通过API获取最近的答案数据
        const response = await fetch(apiUrl);
        const result = await response.json();

        if (result.code === 0) {
          setRecentAnswers(result.data);
        } else {
          console.error('Failed to fetch recent answers:', result.message);
          setRecentAnswers([]);
        }
      } catch (error) {
        console.error('Failed to fetch recent answers:', error);
        setRecentAnswers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentAnswers();
  }, [currentAnswer]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    return date.toLocaleDateString(localeCode, {
      month: "short",
      day: "numeric",
    });
  };

  const getAnswerUrl = (answer: PinpointAnswerFormatted) => {
    if (locale === 'en') {
      return `/${answer.urlSlug}`;
    }
    return `/${locale}/${answer.urlSlug}`;
  };

  const getHistoryUrl = () => {
    if (locale === 'en') {
      return '/history';
    }
    return `/${locale}/history`;
  };

  if (loading) {
    return (
      <div className="apple-card p-8 mb-8">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded flex-1"></div>
                <div className="h-4 bg-gray-200 rounded w-8"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="apple-card p-8 mb-8">
      {/* Header */}
      <div className="mb-6">
        <h2 className="apple-subtitle mb-2">{t('recent_answers')}</h2>
        <p className="apple-body">{t('recent_answers_description')}</p>
      </div>

      {/* Content */}
      {recentAnswers.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Hash className="w-8 h-8" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <p className="apple-body">{t('no_recent_answers')}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recentAnswers.map((answer) => (
            <Link
              key={answer.gameNumber}
              href={getAnswerUrl(answer)}
              className="block group"
            >
              <div className="p-5 rounded-xl transition-all duration-300 hover:scale-[1.02]"
                   style={{
                     backgroundColor: 'var(--color-surface-secondary)',
                     border: '1px solid var(--color-border-light)'
                   }}>
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center"
                             style={{ backgroundColor: 'var(--color-primary)' }}>
                          <Hash className="w-3 h-3 text-white" />
                        </div>
                        <span className="font-semibold text-sm" style={{ color: 'var(--color-primary)' }}>
                          #{answer.gameNumber}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(answer.date)}</span>
                      </div>
                    </div>

                    {/* Mystery Answer Hint */}
                    <div className="mb-3 flex items-center gap-2">
                      <span className="text-xs font-medium" style={{ color: 'var(--color-text-secondary)' }}>
                        {tPinpoint('guess_answer')}
                      </span>
                      <div className="flex gap-1">
                        <span className="inline-block w-6 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                        <span className="inline-block w-8 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                        <span className="inline-block w-4 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
                      </div>
                    </div>

                    {/* Clues */}
                    <div className="space-y-2">
                      <p className="text-xs font-medium" style={{ color: 'var(--color-text-tertiary)' }}>
                        {tPinpoint('clues_label')}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {answer.clues.map((clue, clueIndex) => (
                          <span
                            key={clueIndex}
                            className="inline-block px-3 py-1 rounded-full text-xs font-medium"
                            style={{
                              backgroundColor: 'var(--color-primary)',
                              color: 'white'
                            }}
                          >
                            {clue}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="ml-3 transition-transform duration-300 group-hover:translate-x-1">
                    <ArrowRight className="w-4 h-4" style={{ color: 'var(--color-text-tertiary)' }} />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-6 border-t" style={{ borderColor: 'var(--color-border-light)' }}>
        <Link href={getHistoryUrl()} className="apple-button apple-button-secondary w-full justify-center">
          {t('view_all_history')}
        </Link>
      </div>
    </div>
  );
}
