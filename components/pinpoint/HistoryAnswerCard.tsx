"use client";

import Link from "next/link";
import { useTranslations } from "next-intl";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { Calendar, Hash, ArrowRight } from "lucide-react";

interface HistoryAnswerCardProps {
  answer: PinpointAnswerFormatted;
  locale: string;
}

export default function HistoryAnswerCard({ answer, locale }: HistoryAnswerCardProps) {
  const tPinpoint = useTranslations("pinpoint");

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    return date.toLocaleDateString(localeCode, {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const answerUrl = locale === "en" ? `/${answer.urlSlug}` : `/${locale}/${answer.urlSlug}`;

  return (
    <Link href={answerUrl} className="block group">
      <div className="apple-card p-6 h-full transition-all duration-300 hover:scale-[1.02] hover:shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center"
                 style={{ backgroundColor: 'var(--color-primary)' }}>
              <Hash className="w-5 h-5 text-white" />
            </div>
            <span className="font-semibold apple-body" style={{ color: 'var(--color-primary)' }}>
              #{answer.gameNumber}
            </span>
          </div>
          <div className="flex items-center gap-1 text-sm" style={{ color: 'var(--color-text-secondary)' }}>
            <Calendar className="w-4 h-4" />
            <span>{formatDate(answer.date)}</span>
          </div>
        </div>

        {/* Mystery Answer Hint */}
        <div className="mb-4 flex items-center gap-2">
          <span className="text-sm font-medium" style={{ color: 'var(--color-text-secondary)' }}>
            {tPinpoint("guess_answer")}
          </span>
          <div className="flex gap-1">
            <span className="inline-block w-6 h-2 rounded-sm animate-pulse"
                  style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
            <span className="inline-block w-8 h-2 rounded-sm animate-pulse"
                  style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
            <span className="inline-block w-4 h-2 rounded-sm animate-pulse"
                  style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
          </div>
        </div>

        {/* Clues */}
        <div className="space-y-3 mb-4">
          <p className="text-sm font-medium" style={{ color: 'var(--color-text-tertiary)' }}>
            {tPinpoint("clues_label")}
          </p>
          <div className="flex flex-wrap gap-2">
            {answer.clues.map((clue, clueIndex) => (
              <span
                key={clueIndex}
                className="inline-block px-3 py-1 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'white'
                }}
              >
                {clue}
              </span>
            ))}
          </div>
        </div>

        {/* Arrow */}
        <div className="flex justify-end">
          <div className="transition-transform duration-300 group-hover:translate-x-1">
            <ArrowRight className="w-5 h-5" style={{ color: 'var(--color-text-tertiary)' }} />
          </div>
        </div>
      </div>
    </Link>
  );
}
