"use client";

interface RevealedStageProps {
  answer: string;
  t: any;
}

export default function RevealedStage({ answer, t }: RevealedStageProps) {
  return (
    <div className="space-y-8 animate-fade-in">
      <div className="text-6xl mb-6 animate-bounce">🎉</div>
      <h2 className="apple-title-hero mb-8" style={{ color: 'var(--color-text-primary)' }}>
        {answer}
      </h2>
      <div 
        className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium"
        style={{
          backgroundColor: 'var(--color-primary)',
          color: 'white'
        }}
      >
        {t('answer_revealed')}
      </div>
    </div>
  );
}
