"use client";

interface RevealedStageProps {
  answer: string;
  t: any;
}

export default function RevealedStage({ answer, t }: RevealedStageProps) {
  return (
    <div className="space-y-6 animate-fade-in">
      <div className="text-4xl mb-4 animate-bounce">🎉</div>
      <h2
        className="text-4xl md:text-5xl font-bold mb-6 text-center leading-tight"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          letterSpacing: '-0.02em'
        }}
      >
        {answer}
      </h2>
      <div
        className="inline-flex items-center px-6 py-3 rounded-full text-base font-semibold shadow-lg transform transition-all duration-300 hover:scale-105"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)'
        }}
      >
        ✨ {t('answer_revealed')}
      </div>
    </div>
  );
}
