"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface RevealedStageProps {
  answer: string;
  t: any;
  answerData?: any; // 添加完整的答案数据以获取策略信息
}

export default function RevealedStage({ answer, t, answerData }: RevealedStageProps) {
  const [showStrategy, setShowStrategy] = useState(false);

  // 模拟策略数据 - 在实际应用中应该从answerData获取
  const strategySteps = [
    {
      id: 1,
      icon: "🔍",
      title: "观察线索词",
      content: `分析这5个词：${answerData?.clues?.join('、') || 'book、point、list、mate、please'}`,
      detail: "注意它们的词性、长度和特征"
    },
    {
      id: 2,
      icon: "💡",
      title: "寻找共同点",
      content: "这些词都可以与某个概念相关联",
      detail: "考虑它们在不同语境下的含义"
    },
    {
      id: 3,
      icon: "✅",
      title: "验证答案",
      content: "确认每个线索词都符合答案的逻辑",
      detail: "检查是否有遗漏或例外"
    }
  ];

  const tips = [
    "快速浏览所有线索词，寻找明显的共同特征",
    "考虑词汇的不同含义和用法",
    "思考日常生活中常见的词汇组合",
    "如果卡住了，尝试从不同角度思考每个词",
    "有时答案就是最直接的那个"
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="text-4xl mb-4 animate-bounce">🎉</div>
      <h2
        className="text-4xl md:text-5xl font-bold mb-6 text-center leading-tight"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          letterSpacing: '-0.02em'
        }}
      >
        {answer}
      </h2>
      <div
        className="inline-flex items-center px-6 py-3 rounded-full text-base font-semibold shadow-lg transform transition-all duration-300 hover:scale-105 mb-6"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)'
        }}
      >
        ✨ {t('answer_revealed')}
      </div>

      {/* 解题策略按钮和内容 */}
      <div className="mt-8 pt-6 border-t" style={{ borderColor: 'rgba(102, 126, 234, 0.1)' }}>
        <Button
          onClick={() => setShowStrategy(!showStrategy)}
          className="mb-6 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
          style={{
            background: showStrategy
              ? 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
              : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            color: 'white',
            border: 'none',
            boxShadow: showStrategy
              ? '0 6px 15px rgba(67, 233, 123, 0.3)'
              : '0 6px 15px rgba(240, 147, 251, 0.3)'
          }}
        >
          💡 {showStrategy ? '收起解题思路' : '查看解题思路'}
          {showStrategy ? <ChevronUp className="w-4 h-4 ml-2" /> : <ChevronDown className="w-4 h-4 ml-2" />}
        </Button>

        {/* 可展开的策略内容 */}
        <div
          className={`transition-all duration-500 overflow-hidden ${
            showStrategy ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          {showStrategy && (
            <div className="space-y-6 animate-fade-in">
              {/* 解题步骤 */}
              <div className="space-y-4">
                {strategySteps.map((step, index) => (
                  <div
                    key={step.id}
                    className="p-4 rounded-xl"
                    style={{
                      background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                      border: '1px solid rgba(102, 126, 234, 0.2)'
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0"
                        style={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white'
                        }}
                      >
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold mb-2 flex items-center gap-2">
                          <span className="text-lg">{step.icon}</span>
                          <span style={{ color: '#374151' }}>{step.title}</span>
                        </h4>
                        <p className="text-gray-700 mb-2">{step.content}</p>
                        <p className="text-sm text-gray-600">{step.detail}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 解题小贴士 */}
              <div
                className="p-4 rounded-xl"
                style={{
                  background: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%)',
                  border: '1px solid rgba(67, 233, 123, 0.2)'
                }}
              >
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <span className="text-lg">🎯</span>
                  <span style={{ color: '#374151' }}>解题小贴士</span>
                </h4>
                <ul className="space-y-2 text-sm text-gray-700">
                  {tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-green-500 font-bold">•</span>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
