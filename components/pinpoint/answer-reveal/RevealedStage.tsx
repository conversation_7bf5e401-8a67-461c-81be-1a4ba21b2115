"use client";

interface RevealedStageProps {
  answer: string;
  t: any;
}

export default function RevealedStage({ answer, t }: RevealedStageProps) {
  return (
    <div className="space-y-8 animate-fade-in">
      <div className="text-6xl mb-6 animate-bounce">🎉</div>
      <h2
        className="apple-title-hero mb-8 text-center"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontWeight: '700',
          letterSpacing: '-0.02em'
        }}
      >
        {answer}
      </h2>
      <div
        className="inline-flex items-center px-8 py-4 rounded-full text-lg font-semibold shadow-lg transform transition-all duration-300 hover:scale-105"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          boxShadow: '0 10px 25px rgba(102, 126, 234, 0.3)'
        }}
      >
        ✨ {t('answer_revealed')}
      </div>
    </div>
  );
}
