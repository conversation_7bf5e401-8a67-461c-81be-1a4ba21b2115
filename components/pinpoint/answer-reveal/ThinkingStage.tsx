"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";

interface ThinkingStageProps {
  thinkingTime: number;
  onDirectView: () => void;
  t: any;
}

export default function ThinkingStage({ thinkingTime, onDirectView, t }: ThinkingStageProps) {
  return (
    <div className="space-y-8">
      <div className="text-6xl mb-6 animate-pulse">🧠</div>
      <h2 className="apple-title-hero mb-6">{t('thinking_stage')}</h2>
      <div className="text-4xl font-mono font-bold text-blue-500 mb-4">
        {thinkingTime}
      </div>
      <p className="apple-body mb-8">{t('thinking_prompt')}</p>
      <Button 
        onClick={onDirectView}
        variant="outline"
        className="apple-button apple-button-secondary"
      >
        {t('direct_view')}
      </Button>
    </div>
  );
}
