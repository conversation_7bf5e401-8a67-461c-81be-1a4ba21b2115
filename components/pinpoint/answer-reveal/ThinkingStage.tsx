"use client";

import { But<PERSON> } from "@/components/ui/button";

interface ThinkingStageProps {
  thinkingTime: number;
  onDirectView: () => void;
  t: any;
}

export default function ThinkingStage({ thinkingTime, onDirectView, t }: ThinkingStageProps) {
  return (
    <div className="space-y-6">
      <div className="text-4xl mb-4 animate-pulse">🧠</div>
      <h2
        className="text-3xl md:text-4xl font-bold mb-4"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          letterSpacing: '-0.02em'
        }}
      >
        {t('thinking_stage')}
      </h2>
      <div
        className="text-4xl font-mono font-bold mb-4 animate-pulse"
        style={{
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}
      >
        {thinkingTime}
      </div>
      <p
        className="text-base mb-6 max-w-md mx-auto leading-relaxed"
        style={{
          color: '#6b7280',
          fontWeight: '500'
        }}
      >
        {t('thinking_prompt')}
      </p>
      <Button
        onClick={onDirectView}
        className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:-translate-y-1"
        style={{
          background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
          color: 'white',
          border: 'none',
          boxShadow: '0 6px 15px rgba(67, 233, 123, 0.3)'
        }}
      >
        ⚡ {t('direct_view')}
      </Button>
    </div>
  );
}
