"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";

export type RevealStage = 'hidden' | 'thinking' | 'revealed';

interface UseAnswerRevealProps {
  onRevealClick?: () => void;
  onAnswerRevealed?: () => void;
}

export function useAnswerReveal({ onRevealClick, onAnswerRevealed }: UseAnswerRevealProps = {}) {
  const [stage, setStage] = useState<RevealStage>('hidden');
  const [thinkingTime, setThinkingTime] = useState(5);
  const [selectedHint, setSelectedHint] = useState<string>('');
  
  const t = useTranslations('answer_reveal');
  
  // 随机选择思考提示
  useEffect(() => {
    const hints = t.raw('thinking_hints') as string[];
    const randomHint = hints[Math.floor(Math.random() * hints.length)];
    setSelectedHint(randomHint);
  }, [t]);

  // 思考阶段倒计时
  useEffect(() => {
    if (stage === 'thinking' && thinkingTime > 0) {
      const timer = setTimeout(() => setThinkingTime(prev => prev - 1), 1000);
      return () => clearTimeout(timer);
    } else if (stage === 'thinking' && thinkingTime === 0) {
      setStage('revealed');
      onAnswerRevealed?.();
    }
  }, [stage, thinkingTime, onAnswerRevealed]);

  const handleRevealClick = () => {
    setStage('thinking');
    onRevealClick?.();
    
    // 用户行为追踪
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'answer_reveal_click', {
        engagement_type: 'answer_interaction'
      });
    }
  };

  const handleDirectView = () => {
    setStage('revealed');
    onAnswerRevealed?.();
    
    // 用户行为追踪
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'answer_direct_view', {
        engagement_type: 'answer_interaction'
      });
    }
  };

  const resetReveal = () => {
    setStage('hidden');
    setThinkingTime(5);
  };

  return {
    stage,
    thinkingTime,
    selectedHint,
    handleRevealClick,
    handleDirectView,
    resetReveal,
    t
  };
}
