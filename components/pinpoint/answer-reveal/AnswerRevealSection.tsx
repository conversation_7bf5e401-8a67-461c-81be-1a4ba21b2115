"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { useAnswerReveal } from "./useAnswerReveal";
import ThinkingStage from "./ThinkingStage";
import RevealedStage from "./RevealedStage";
import { useTranslations } from "next-intl";

interface AnswerRevealSectionProps {
  answer: PinpointAnswerFormatted;
}

export default function AnswerRevealSection({ answer }: AnswerRevealSectionProps) {
  const {
    stage,
    thinkingTime,
    selectedHint,
    handleRevealClick,
    handleDirectView,
    t
  } = useAnswerReveal();

  const tPinpoint = useTranslations('pinpoint');

  return (
    <div className="relative">
      {/* SEO内容层 - 始终在HTML中但隐藏，仅供搜索引擎抓取 */}
      <div
        className="seo-content sr-only"
        aria-hidden="true"
      >
        <div className="apple-card p-12 mb-12 text-center">
          <h2 className="apple-title-hero mb-8">
            {answer.answer}
          </h2>
          <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium">
            {t('answer_revealed')}
          </div>
        </div>
      </div>

      {/* 用户交互层 - 用户实际看到的内容 */}
      <div className="apple-card p-6 mb-6">
        {/* 答案展示区域 */}
        <div className="text-center min-h-[200px] flex items-center justify-center mb-6">
          {stage === 'hidden' && (
            <div className="space-y-6">
              <div className="text-4xl mb-4 animate-pulse">🤔</div>
              <h2
                className="text-3xl md:text-4xl font-bold mb-4"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  letterSpacing: '-0.02em'
                }}
              >
                {t('ready_title')}
              </h2>
              <p
                className="text-lg mb-6 max-w-lg mx-auto leading-relaxed"
                style={{
                  color: '#6b7280',
                  fontWeight: '500'
                }}
              >
                {selectedHint}
              </p>
              <Button
                onClick={handleRevealClick}
                className="text-lg px-8 py-4 rounded-xl font-bold shadow-lg transform transition-all duration-300 hover:scale-105 hover:-translate-y-1"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  boxShadow: '0 10px 25px rgba(102, 126, 234, 0.4)'
                }}
              >
                🎯 {t('reveal_button')}
              </Button>
            </div>
          )}

          {stage === 'thinking' && (
            <ThinkingStage
              thinkingTime={thinkingTime}
              onDirectView={handleDirectView}
              t={t}
            />
          )}

          {stage === 'revealed' && (
            <RevealedStage answer={answer.answer} t={t} answerData={answer} />
          )}
        </div>

        {/* 线索词展示 - 始终显示 */}
        <div className="border-t pt-6" style={{ borderColor: 'rgba(102, 126, 234, 0.1)' }}>
          <h3
            className="text-center mb-6"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontWeight: '600',
              fontSize: '1.75rem',
              letterSpacing: '-0.01em'
            }}
          >
            {tPinpoint('clues')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {answer.clues.map((clue, index) => {
              const gradients = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
              ];

              return (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-xl p-5 text-center transition-all duration-500 hover:scale-105 hover:-translate-y-1 group shadow-lg"
                  style={{
                    background: gradients[index % gradients.length],
                    color: 'white',
                    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <div className="opacity-90 mb-2 text-xs font-medium">
                    {tPinpoint('clue_number', { number: index + 1 })}
                  </div>
                  <div className="text-lg font-bold tracking-wide">{clue}</div>
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full opacity-30 group-hover:opacity-50 transition-opacity duration-500"
                       style={{ backgroundColor: 'white' }}></div>
                  <div className="absolute -bottom-1 -left-1 w-4 h-4 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"
                       style={{ backgroundColor: 'white' }}></div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
