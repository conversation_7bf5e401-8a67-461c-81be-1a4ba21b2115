"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { useAnswerReveal } from "./useAnswerReveal";
import ThinkingStage from "./ThinkingStage";
import RevealedStage from "./RevealedStage";
import { useTranslations } from "next-intl";

interface AnswerRevealSectionProps {
  answer: PinpointAnswerFormatted;
}

export default function AnswerRevealSection({ answer }: AnswerRevealSectionProps) {
  const {
    stage,
    thinkingTime,
    selectedHint,
    handleRevealClick,
    handleDirectView,
    t
  } = useAnswerReveal();

  const tPinpoint = useTranslations('pinpoint');

  return (
    <div className="relative">
      {/* SEO内容层 - 始终在HTML中但隐藏，仅供搜索引擎抓取 */}
      <div
        className="seo-content sr-only"
        aria-hidden="true"
      >
        <div className="apple-card p-12 mb-12 text-center">
          <h2 className="apple-title-hero mb-8">
            {answer.answer}
          </h2>
          <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium">
            {t('answer_revealed')}
          </div>
        </div>
      </div>

      {/* 用户交互层 - 用户实际看到的内容 */}
      <div className="apple-card p-8 mb-8">
        {/* 答案展示区域 */}
        <div className="text-center min-h-[250px] flex items-center justify-center mb-8">
          {stage === 'hidden' && (
            <div className="space-y-6">
              <div className="text-5xl mb-4">🤔</div>
              <h2 className="apple-title-hero mb-4">{t('ready_title')}</h2>
              <p className="apple-body text-lg mb-6 max-w-md mx-auto">
                {selectedHint}
              </p>
              <Button
                onClick={handleRevealClick}
                className="apple-button-primary text-lg px-8 py-4 hover:scale-105 transition-transform"
              >
                {t('reveal_button')}
              </Button>
            </div>
          )}

          {stage === 'thinking' && (
            <ThinkingStage
              thinkingTime={thinkingTime}
              onDirectView={handleDirectView}
              t={t}
            />
          )}

          {stage === 'revealed' && (
            <RevealedStage answer={answer.answer} t={t} />
          )}
        </div>

        {/* 线索词展示 - 始终显示 */}
        <div className="border-t pt-8">
          <h3 className="apple-title text-center mb-6">{tPinpoint('clues')}</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {answer.clues.map((clue, index) => (
              <div
                key={index}
                className="relative overflow-hidden rounded-xl p-4 text-center transition-all duration-500 hover:scale-105 group"
                style={{
                  background: 'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)',
                  color: 'white'
                }}
              >
                <div className="apple-caption opacity-80 mb-1 text-xs">{tPinpoint('clue_number', { number: index + 1 })}</div>
                <div className="text-lg font-semibold">{clue}</div>
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full opacity-20"
                     style={{ backgroundColor: 'white' }}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
