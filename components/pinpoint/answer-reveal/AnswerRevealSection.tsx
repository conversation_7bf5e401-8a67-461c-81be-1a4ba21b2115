"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { useAnswerReveal } from "./useAnswerReveal";
import ThinkingStage from "./ThinkingStage";
import RevealedStage from "./RevealedStage";

interface AnswerRevealSectionProps {
  answer: PinpointAnswerFormatted;
}

export default function AnswerRevealSection({ answer }: AnswerRevealSectionProps) {
  const { 
    stage, 
    thinkingTime, 
    selectedHint, 
    handleRevealClick, 
    handleDirectView, 
    t 
  } = useAnswerReveal();

  return (
    <div className="relative">
      {/* SEO内容层 - 始终在HTML中但隐藏，仅供搜索引擎抓取 */}
      <div
        className="seo-content sr-only"
        aria-hidden="true"
      >
        <div className="apple-card p-12 mb-12 text-center">
          <h2 className="apple-title-hero mb-8">
            {answer.answer}
          </h2>
          <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium">
            {t('answer_revealed')}
          </div>
        </div>
      </div>

      {/* 用户交互层 - 用户实际看到的内容 */}
      <div className="apple-card p-12 mb-12 text-center min-h-[300px] flex items-center justify-center">
        {stage === 'hidden' && (
          <div className="space-y-8">
            <div className="text-6xl mb-6">🤔</div>
            <h2 className="apple-title-hero mb-6">{t('ready_title')}</h2>
            <p className="apple-body text-lg mb-8 max-w-md mx-auto">
              {selectedHint}
              <span className="text-sm text-gray-500 mt-2 block">
                {t('clue_preview', { clues: answer.clues.join(' • ') })}
              </span>
            </p>
            <Button
              onClick={handleRevealClick}
              className="apple-button-primary text-lg px-8 py-4 hover:scale-105 transition-transform"
            >
              {t('reveal_button')}
            </Button>
          </div>
        )}

        {stage === 'thinking' && (
          <ThinkingStage
            thinkingTime={thinkingTime}
            onDirectView={handleDirectView}
            t={t}
          />
        )}

        {stage === 'revealed' && (
          <div className="space-y-8">
            <h2 className="apple-title-hero mb-8" style={{ color: 'var(--color-text-primary)' }}>
              {answer.answer}
            </h2>
            <div
              className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium"
              style={{
                backgroundColor: 'var(--color-primary)',
                color: 'white'
              }}
            >
              🎉 {t('answer_revealed')}
            </div>
            <RevealedStage answer={answer.answer} t={t} />
          </div>
        )}
      </div>
    </div>
  );
}
