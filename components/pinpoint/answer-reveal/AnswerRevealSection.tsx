"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { useAnswerReveal } from "./useAnswerReveal";
import ThinkingStage from "./ThinkingStage";
import RevealedStage from "./RevealedStage";
import { useTranslations } from "next-intl";

interface AnswerRevealSectionProps {
  answer: PinpointAnswerFormatted;
}

export default function AnswerRevealSection({ answer }: AnswerRevealSectionProps) {
  const {
    stage,
    thinkingTime,
    selectedHint,
    handleRevealClick,
    handleDirectView,
    t
  } = useAnswerReveal();

  const tPinpoint = useTranslations('pinpoint');

  return (
    <div className="relative">
      {/* SEO内容层 - 始终在HTML中但隐藏，仅供搜索引擎抓取 */}
      <div
        className="seo-content sr-only"
        aria-hidden="true"
      >
        <div className="apple-card p-12 mb-12 text-center">
          <h2 className="apple-title-hero mb-8">
            {answer.answer}
          </h2>
          <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium">
            {t('answer_revealed')}
          </div>
        </div>
      </div>

      {/* 用户交互层 - 用户实际看到的内容 */}
      <div className="apple-card p-8 mb-8">
        {/* 答案展示区域 */}
        <div className="text-center min-h-[250px] flex items-center justify-center mb-8">
          {stage === 'hidden' && (
            <div className="space-y-8">
              <div className="text-6xl mb-6 animate-pulse">🤔</div>
              <h2
                className="apple-title-hero mb-6"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  fontWeight: '700',
                  letterSpacing: '-0.02em'
                }}
              >
                {t('ready_title')}
              </h2>
              <p
                className="text-xl mb-8 max-w-lg mx-auto leading-relaxed"
                style={{
                  color: '#6b7280',
                  fontWeight: '500'
                }}
              >
                {selectedHint}
              </p>
              <Button
                onClick={handleRevealClick}
                className="text-xl px-10 py-5 rounded-2xl font-bold shadow-xl transform transition-all duration-300 hover:scale-110 hover:-translate-y-1"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  boxShadow: '0 15px 35px rgba(102, 126, 234, 0.4)'
                }}
              >
                🎯 {t('reveal_button')}
              </Button>
            </div>
          )}

          {stage === 'thinking' && (
            <ThinkingStage
              thinkingTime={thinkingTime}
              onDirectView={handleDirectView}
              t={t}
            />
          )}

          {stage === 'revealed' && (
            <RevealedStage answer={answer.answer} t={t} />
          )}
        </div>

        {/* 线索词展示 - 始终显示 */}
        <div className="border-t pt-8" style={{ borderColor: 'rgba(102, 126, 234, 0.1)' }}>
          <h3
            className="apple-title text-center mb-8"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontWeight: '600',
              fontSize: '2rem',
              letterSpacing: '-0.01em'
            }}
          >
            {tPinpoint('clues')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
            {answer.clues.map((clue, index) => {
              const gradients = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
              ];

              return (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-2xl p-6 text-center transition-all duration-500 hover:scale-110 hover:-translate-y-2 group shadow-lg"
                  style={{
                    background: gradients[index % gradients.length],
                    color: 'white',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <div className="apple-caption opacity-90 mb-2 text-sm font-medium">
                    {tPinpoint('clue_number', { number: index + 1 })}
                  </div>
                  <div className="text-xl font-bold tracking-wide">{clue}</div>
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-2xl"></div>
                  <div className="absolute -top-3 -right-3 w-8 h-8 rounded-full opacity-30 group-hover:opacity-50 transition-opacity duration-500"
                       style={{ backgroundColor: 'white' }}></div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"
                       style={{ backgroundColor: 'white' }}></div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
