"use client";

import { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { Search, ChevronLeft, ChevronRight } from "lucide-react";
import HistoryAnswerCard from "./HistoryAnswerCard";

interface ClientSearchableHistoryProps {
  allAnswers: PinpointAnswerFormatted[];
  locale: string;
}

export default function ClientSearchableHistory({ 
  allAnswers, 
  locale 
}: ClientSearchableHistoryProps) {
  const searchParams = useSearchParams();
  const initialSearch = searchParams.get("search") || "";
  
  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  const t = useTranslations("history");

  // 当URL参数变化时更新搜索查询
  useEffect(() => {
    const urlSearch = searchParams.get("search") || "";
    setSearchQuery(urlSearch);
    setCurrentPage(1); // 重置到第一页
  }, [searchParams]);

  // 过滤和分页逻辑
  const { filteredAnswers, paginatedAnswers, totalPages } = useMemo(() => {
    let filtered = allAnswers;
    
    if (searchQuery && searchQuery.trim()) {
      const searchLower = searchQuery.trim().toLowerCase();
      filtered = allAnswers.filter((answer) => {
        const answerMatch = answer.answer.toLowerCase().includes(searchLower);
        const cluesMatch = answer.clues.some((clue) => 
          clue.toLowerCase().includes(searchLower)
        );
        const gameNumberMatch = answer.gameNumber.toString().includes(searchLower);
        const dateMatch = answer.date.includes(searchLower);
        
        return answerMatch || cluesMatch || gameNumberMatch || dateMatch;
      });
    }

    const totalPages = Math.ceil(filtered.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const paginated = filtered.slice(startIndex, startIndex + pageSize);

    return {
      filteredAnswers: filtered,
      paginatedAnswers: paginated,
      totalPages
    };
  }, [allAnswers, searchQuery, currentPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    
    // 更新URL但不刷新页面
    const url = new URL(window.location.href);
    if (searchQuery && searchQuery.trim()) {
      url.searchParams.set("search", searchQuery.trim());
    } else {
      url.searchParams.delete("search");
    }
    url.searchParams.delete("page");
    
    window.history.pushState({}, "", url.toString());
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const showPages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
    let endPage = Math.min(totalPages, startPage + showPages - 1);

    if (endPage - startPage + 1 < showPages) {
      startPage = Math.max(1, endPage - showPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-center gap-2 mt-12">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="apple-button apple-button-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4" />
          {t("previous_page")}
        </button>

        <div className="flex gap-1">
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                page === currentPage
                  ? "bg-blue-600 text-white"
                  : "hover:bg-gray-100"
              }`}
            >
              {page}
            </button>
          ))}
        </div>

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="apple-button apple-button-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t("next_page")}
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    );
  };

  return (
    <div>
      {/* 搜索栏 */}
      <form onSubmit={handleSearch} className="max-w-lg mx-auto mb-8">
        <div className="relative flex items-center gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5"
                    style={{ color: 'var(--color-text-tertiary)' }} />
            <input
              type="text"
              placeholder={t("search_placeholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="apple-input pl-12 pr-4 py-4 text-lg w-full"
            />
          </div>
          <button
            type="submit"
            className="apple-button apple-button-primary px-6 py-4 text-lg flex-shrink-0"
          >
            {t("search_button")}
          </button>
        </div>
      </form>

      {/* 搜索结果统计 */}
      <div className="text-center mb-8">
        {searchQuery ? (
          <div>
            <p className="apple-body mb-2">
              {t("search_query", { query: searchQuery })}
            </p>
            <p className="apple-body-small" style={{ color: 'var(--color-text-secondary)' }}>
              {t("total_answers", { count: filteredAnswers.length })}
            </p>
          </div>
        ) : (
          <p className="apple-body-small" style={{ color: 'var(--color-text-secondary)' }}>
            {t("total_answers", { count: allAnswers.length })}
          </p>
        )}
      </div>

      {/* 答案列表 */}
      {paginatedAnswers.length === 0 ? (
        <div className="apple-card p-16 text-center">
          <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Search className="w-10 h-10" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <h3 className="apple-subtitle mb-4">
            {searchQuery ? t("no_results") : t("no_answers")}
          </h3>
          <p className="apple-body mb-8">
            {searchQuery ? t("try_other_keywords") : t("will_show_here")}
          </p>
          {searchQuery && (
            <button 
              onClick={() => {
                setSearchQuery("");
                setCurrentPage(1);
                window.history.pushState({}, "", window.location.pathname);
              }}
              className="apple-button apple-button-primary"
            >
              {t("view_all_answers")}
            </button>
          )}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {paginatedAnswers.map((answer) => (
              <HistoryAnswerCard 
                key={answer.gameNumber} 
                answer={answer} 
                locale={locale}
              />
            ))}
          </div>
          {renderPagination()}
        </>
      )}
    </div>
  );
}
