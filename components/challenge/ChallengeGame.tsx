"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { ArrowLef<PERSON>, Trophy, Target } from "lucide-react";
import { useRouter } from "next/navigation";

interface Question {
  id: string;
  gameNumber: number;
  answer: string;
  clues: string[];
  date: string;
}

interface GameState {
  currentQuestion: number;
  score: number;
  streak: number;
  maxStreak: number;
  answers: string[];
  userAnswers: string[];
  isComplete: boolean;
  startTime: number;
}

export default function ChallengeGame() {
  const [gameState, setGameState] = useState<GameState>({
    currentQuestion: 0,
    score: 0,
    streak: 0,
    maxStreak: 0,
    answers: [],
    userAnswers: [],
    isComplete: false,
    startTime: Date.now()
  });
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [userAnswer, setUserAnswer] = useState("");
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  
  const t = useTranslations('challenge');
  const router = useRouter();

  // 模拟题目数据 - 实际应该从API获取
  useEffect(() => {
    const mockQuestions: Question[] = [
      {
        id: "1",
        gameNumber: 455,
        answer: "Words that come before 'line'",
        clues: ["book", "point", "list", "mate", "please"],
        date: "2025-01-08"
      },
      {
        id: "2", 
        gameNumber: 454,
        answer: "Types of pasta",
        clues: ["penne", "fusilli", "rigatoni", "farfalle", "linguine"],
        date: "2025-01-07"
      },
      // 可以添加更多题目
    ];
    
    // 随机打乱题目顺序
    const shuffled = [...mockQuestions].sort(() => Math.random() - 0.5);
    setQuestions(shuffled.slice(0, 10)); // 取前10题
  }, []);

  const currentQuestion = questions[gameState.currentQuestion];

  const handleSubmitAnswer = () => {
    if (!userAnswer.trim() || !currentQuestion) return;

    const correct = userAnswer.toLowerCase().trim() === currentQuestion.answer.toLowerCase().trim();
    setIsCorrect(correct);
    setShowResult(true);

    const newStreak = correct ? gameState.streak + 1 : 0;
    const newScore = correct ? gameState.score + 1 : gameState.score;
    const newMaxStreak = Math.max(gameState.maxStreak, newStreak);

    setGameState(prev => ({
      ...prev,
      score: newScore,
      streak: newStreak,
      maxStreak: newMaxStreak,
      answers: [...prev.answers, currentQuestion.answer],
      userAnswers: [...prev.userAnswers, userAnswer]
    }));
  };

  const handleNextQuestion = () => {
    const nextIndex = gameState.currentQuestion + 1;
    
    if (nextIndex >= questions.length) {
      // 游戏结束
      setGameState(prev => ({ ...prev, isComplete: true }));
    } else {
      // 下一题
      setGameState(prev => ({ ...prev, currentQuestion: nextIndex }));
      setUserAnswer("");
      setShowResult(false);
    }
  };

  const handleRestart = () => {
    setGameState({
      currentQuestion: 0,
      score: 0,
      streak: 0,
      maxStreak: 0,
      answers: [],
      userAnswers: [],
      isComplete: false,
      startTime: Date.now()
    });
    setUserAnswer("");
    setShowResult(false);
    
    // 重新打乱题目
    const shuffled = [...questions].sort(() => Math.random() - 0.5);
    setQuestions(shuffled);
  };

  const getStreakEmoji = (streak: number) => {
    if (streak >= 7) return "🔥🔥🔥";
    if (streak >= 5) return "🔥🔥";
    if (streak >= 3) return "🔥";
    return "";
  };

  const getStreakText = (streak: number) => {
    if (streak >= 7) return t("streak_unstoppable");
    if (streak >= 5) return t("streak_fire");
    if (streak >= 3) return t("streak_good");
    return "";
  };

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>{t("loading")}</p>
        </div>
      </div>
    );
  }

  if (gameState.isComplete) {
    const totalTime = Math.floor((Date.now() - gameState.startTime) / 1000);
    const minutes = Math.floor(totalTime / 60);
    const seconds = totalTime % 60;

    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-6">🎉</div>
            <h1 className="text-3xl font-bold mb-6" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              {t("challenge_complete")}
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-blue-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-blue-600">{gameState.score}/{questions.length}</div>
                <div className="text-sm text-gray-600">最终得分</div>
              </div>
              <div className="bg-orange-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-orange-600">{gameState.maxStreak} {getStreakEmoji(gameState.maxStreak)}</div>
                <div className="text-sm text-gray-600">最长连胜</div>
              </div>
              <div className="bg-green-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-green-600">{minutes}:{seconds.toString().padStart(2, '0')}</div>
                <div className="text-sm text-gray-600">用时</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleRestart}
                className="px-6 py-3 rounded-xl font-semibold"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none'
                }}
              >
                <Trophy className="w-4 h-4 mr-2" />
                再来一轮
              </Button>
              <Button
                onClick={() => router.back()}
                variant="outline"
                className="px-6 py-3 rounded-xl font-semibold"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* 头部信息 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <Button
              onClick={() => router.back()}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回
            </Button>
            <div className="text-sm text-gray-600">
              题目 {gameState.currentQuestion + 1}/{questions.length}
            </div>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="text-sm">
                <span className="text-gray-600">得分: </span>
                <span className="font-semibold text-blue-600">{gameState.score}</span>
              </div>
              {gameState.streak > 0 && (
                <div className="text-sm">
                  <span className="text-gray-600">连胜: </span>
                  <span className="font-semibold text-orange-600">
                    {gameState.streak} {getStreakEmoji(gameState.streak)}
                  </span>
                  {getStreakText(gameState.streak) && (
                    <span className="ml-2 text-xs text-orange-500">{getStreakText(gameState.streak)}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 题目卡片 */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          {!showResult ? (
            <>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-6">今天的线索词是什么？</h2>
                
                {/* 线索词展示 */}
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
                  {currentQuestion.clues.map((clue, index) => (
                    <div
                      key={index}
                      className="bg-blue-50 rounded-xl p-4 text-center"
                    >
                      <div className="text-lg font-semibold text-blue-800">{clue}</div>
                    </div>
                  ))}
                </div>

                <div className="mb-6">
                  <label className="block text-lg font-medium mb-4">你的答案是？</label>
                  <input
                    type="text"
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSubmitAnswer()}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none text-center text-lg"
                    placeholder="输入你的答案..."
                    autoFocus
                  />
                </div>

                <Button
                  onClick={handleSubmitAnswer}
                  disabled={!userAnswer.trim()}
                  className="px-8 py-3 rounded-xl font-semibold"
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none'
                  }}
                >
                  <Target className="w-4 h-4 mr-2" />
                  提交答案
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center">
              <div className="text-6xl mb-4">
                {isCorrect ? "🎉" : "😅"}
              </div>
              <h3 className="text-2xl font-bold mb-4">
                {isCorrect ? "答对了！" : "答错了"}
              </h3>
              <div className="bg-gray-50 rounded-xl p-4 mb-6">
                <div className="text-sm text-gray-600 mb-2">正确答案：</div>
                <div className="text-xl font-semibold">{currentQuestion.answer}</div>
              </div>
              <Button
                onClick={handleNextQuestion}
                className="px-8 py-3 rounded-xl font-semibold"
                style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  color: 'white',
                  border: 'none'
                }}
              >
                {gameState.currentQuestion + 1 >= questions.length ? "查看结果" : "下一题"}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
