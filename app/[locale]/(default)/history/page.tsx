import { Metadata } from "next";
import { setRequestLocale, getTranslations } from "next-intl/server";
import { getAllPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import ClientSearchableHistory from "@/components/pinpoint/ClientSearchableHistory";

// 配置ISR - 保持SEO效果，搜索功能通过客户端实现
export const revalidate = 3600;
export const dynamic = "force-static";

// 生成页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations("history");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/history`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/history`;
  }

  const title = t("meta_title");
  const description = t("meta_description");

  return {
    title,
    description,
    keywords: [
      "LinkedIn Pinpoint",
      locale === "zh" ? "历史答案" : "Answer History",
      locale === "zh" ? "Pinpoint答案查询" : "Pinpoint Answer Search",
      locale === "zh" ? "游戏攻略" : "Game Guide",
      locale === "zh" ? "文字游戏" : "Word Game",
      locale === "zh" ? "线索词" : "Clue Words",
      locale === "zh" ? "答案列表" : "Answer List",
    ],
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: locale === "zh" ? "LinkedIn Pinpoint 历史答案" : "LinkedIn Pinpoint Answer History",
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// 历史答案页面组件
export default async function HistoryPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations("history");

  try {
    // 获取所有答案
    const allAnswers = await getAllPinpointAnswers();
    const formattedAnswers = allAnswers.map(formatPinpointAnswer);

    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        <div className="container mx-auto px-6 py-8">
          {/* 导航栏 */}
          <div className="mb-12">
            <Link href={locale === "en" ? "/" : `/${locale}`} className="apple-button apple-button-secondary">
              <ArrowLeft className="w-4 h-4" />
              {t("back_to_home")}
            </Link>
          </div>

          {/* 页面标题 */}
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="apple-title-large mb-6">{t("title")}</h1>
              <p className="apple-body-large mb-12 max-w-2xl mx-auto">
                {t("description")}
              </p>

              {/* 客户端搜索组件 */}
              <ClientSearchableHistory
                allAnswers={formattedAnswers}
                locale={locale}
              />
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("加载历史答案失败:", error);

    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        <div className="container mx-auto px-6 py-8">
          <div className="mb-12">
            <Link href={locale === "en" ? "/" : `/${locale}`} className="apple-button apple-button-secondary">
              <ArrowLeft className="w-4 h-4" />
              {t("back_to_home")}
            </Link>
          </div>

          <div className="max-w-7xl mx-auto text-center">
            <h1 className="apple-title-large mb-6">{t("title")}</h1>
            <div className="apple-card p-16">
              <p className="apple-body text-red-600">{t("error_loading")}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
