import { Metadata } from "next";
import { notFound } from "next/navigation";
import { setRequestLocale, getTranslations } from "next-intl/server";
import { getAllPinpointAnswers, findPinpointAnswerBySlug, getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer, generateSEOData } from "@/lib/pinpoint";
import PinpointAnswerPage from "@/components/pinpoint/PinpointAnswerPage";
import EnhancedPinpointAnswerPage from "@/components/pinpoint/enhanced/EnhancedPinpointAnswerPage";
import JsonLd from "@/components/seo/JsonLd";

// 配置ISR - 每小时重新生成
export const revalidate = 3600;
export const dynamic = "force-static";
export const dynamicParams = true;

// 生成静态参数
export async function generateStaticParams({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  try {
    const answers = await getAllPinpointAnswers();
    return answers.map((answer) => ({
      slug: answer.url_slug,
    }));
  } catch (error) {
    console.error("生成静态参数失败:", error);
    return [];
  }
}

// 生成页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  setRequestLocale(locale);

  try {
    const answer = await findPinpointAnswerBySlug(slug);

    if (!answer) {
      const t = await getTranslations("answer_detail");
      return {
        title: t("not_found_title"),
        description: t("not_found_description"),
      };
    }

    const formattedAnswer = formatPinpointAnswer(answer);
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://pinpointodays.com";
    const seoData = generateSEOData(formattedAnswer, baseUrl, locale);

    let canonicalUrl = `${baseUrl}/${slug}`;
    if (locale !== "en") {
      canonicalUrl = `${baseUrl}/${locale}/${slug}`;
    }

    const siteName = locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers";
    const authorName = locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers";

    return {
      title: seoData.title,
      description: seoData.description,
      keywords: seoData.keywords,
      openGraph: {
        title: seoData.title,
        description: seoData.description,
        url: canonicalUrl,
        siteName,
        images: [
          {
            url: `${baseUrl}/og-image.png`,
            width: 1200,
            height: 630,
            alt: seoData.title,
          },
        ],
        locale: locale === "zh" ? "zh_CN" : "en_US",
        type: "article",
        publishedTime: answer.created_at,
        modifiedTime: answer.updated_at,
        authors: [authorName],
        tags: seoData.keywords,
      },
      twitter: {
        card: "summary_large_image",
        title: seoData.title,
        description: seoData.description,
        images: [`${baseUrl}/og-image.png`],
        creator: "@pinpointodays",
        site: "@pinpointodays",
      },
      alternates: {
        canonical: canonicalUrl,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    };
  } catch (error) {
    console.error("生成元数据失败:", error);
    const title = locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers";
    const description = locale === "zh"
      ? "获取LinkedIn Pinpoint每日答案和解析。"
      : "Get LinkedIn Pinpoint daily answers and analysis.";

    return {
      title,
      description,
    };
  }
}

// 答案详情页面组件
export default async function AnswerDetailPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;
  setRequestLocale(locale);

  try {
    const answer = await findPinpointAnswerBySlug(slug);

    if (!answer) {
      notFound();
    }

    const formattedAnswer = formatPinpointAnswer(answer);

    // 获取最近的答案数据（排除当前答案）
    const recentAnswersData = await getRecentPinpointAnswers(4); // 获取4个，以防当前答案在其中
    const recentAnswers = recentAnswersData
      .filter(item => item.game_number !== answer.game_number) // 排除当前答案
      .slice(0, 3) // 只取3个
      .map(formatPinpointAnswer);

    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://pinpointodays.com";

    // 生成JSON-LD结构化数据
    const siteName = locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers";
    const answerLabel = locale === "zh" ? "答案" : "Answer";
    const cluesSeparator = locale === "zh" ? "、" : ", ";
    const articleSection = locale === "zh" ? "游戏攻略" : "Game Guide";

    const headline = `Pinpoint #${formattedAnswer.gameNumber} ${answerLabel}: ${formattedAnswer.answer}`;
    const description = locale === "zh"
      ? `LinkedIn Pinpoint 第${formattedAnswer.gameNumber}期答案揭晓！答案是"${formattedAnswer.answer}"，线索词包括：${formattedAnswer.clues.join(cluesSeparator)}。`
      : `LinkedIn Pinpoint #${formattedAnswer.gameNumber} answer revealed! The answer is "${formattedAnswer.answer}", clues include: ${formattedAnswer.clues.join(cluesSeparator)}.`;

    const keywords = [
      "LinkedIn Pinpoint",
      locale === "zh" ? "Pinpoint答案" : "Pinpoint Answer",
      `Pinpoint ${formattedAnswer.gameNumber}`,
      formattedAnswer.answer,
      ...formattedAnswer.clues,
    ];

    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "Article",
      headline,
      description,
      author: {
        "@type": "Organization",
        name: siteName,
        url: baseUrl,
      },
      publisher: {
        "@type": "Organization",
        name: siteName,
        url: baseUrl,
        logo: {
          "@type": "ImageObject",
          url: `${baseUrl}/logo.png`,
        },
      },
      datePublished: answer.created_at,
      dateModified: answer.updated_at,
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": `${baseUrl}/${locale === "en" ? "" : locale + "/"}${slug}`,
      },
      image: `${baseUrl}/og-image.png`,
      keywords: keywords.join(", "),
      articleSection,
      wordCount: 200,
      inLanguage: locale === "zh" ? "zh-CN" : "en-US",
    };

    return (
      <>
        <JsonLd data={jsonLd} />
        <EnhancedPinpointAnswerPage answer={formattedAnswer} />
      </>
    );
  } catch (error) {
    console.error("加载答案详情失败:", error);
    notFound();
  }
}
