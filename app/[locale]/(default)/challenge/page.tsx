import { Metadata } from "next";
import { setRequestLocale, getTranslations } from "next-intl/server";
import ChallengeGame from "@/components/challenge/ChallengeGame";

interface ChallengePageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({ params }: ChallengePageProps): Promise<Metadata> {
  const { locale } = params;
  setRequestLocale(locale);
  
  const t = await getTranslations("challenge");
  
  return {
    title: locale === "zh" ? "Pinpoint 挑战模式 - 测试你的解题技能" : "Pinpoint Challenge - Test Your Skills",
    description: locale === "zh" 
      ? "通过随机的 LinkedIn Pinpoint 题目挑战自己，测试你的解题技能和反应速度。"
      : "Challenge yourself with random LinkedIn Pinpoint puzzles and test your solving skills.",
  };
}

export default function ChallengePage({ params }: ChallengePageProps) {
  const { locale } = params;
  setRequestLocale(locale);

  return <ChallengeGame />;
}
