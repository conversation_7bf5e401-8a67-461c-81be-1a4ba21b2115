@import "tailwindcss";
@import "./theme.css";
@import "../styles/apple-design-system.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

/* 用户参与度优化动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* 交互层样式 */
.seo-content {
  /* 确保SEO内容始终存在于DOM中 */
  position: relative;
}

.interactive-overlay {
  /* 交互层覆盖在SEO内容上 */
  position: relative;
  background: inherit;
}

/* 展开/收起动画 */
.expandable-content {
  transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .apple-title-hero {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
