import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get("limit") || "5");
    const excludeGameNumber = searchParams.get("exclude");

    // 获取最近的答案数据
    const recentAnswers = await getRecentPinpointAnswers(limit + (excludeGameNumber ? 1 : 0));
    
    // 格式化数据
    let formattedAnswers = recentAnswers.map(formatPinpointAnswer);
    
    // 如果需要排除特定的游戏编号
    if (excludeGameNumber) {
      formattedAnswers = formattedAnswers.filter(
        answer => answer.gameNumber !== parseInt(excludeGameNumber)
      );
    }
    
    // 限制返回数量
    formattedAnswers = formattedAnswers.slice(0, limit);

    return respData(formattedAnswers);
  } catch (error) {
    console.error("获取最近答案失败:", error);
    return respErr("获取最近答案失败");
  }
}
