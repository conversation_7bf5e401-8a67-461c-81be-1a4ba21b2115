import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getRecentPinpointAnswers, getAllPinpointAnswers } from "@/models/pinpoint";

export async function GET(req: NextRequest) {
  try {
    console.log("🔍 Testing database connection...");
    
    // 测试获取所有答案
    const allAnswers = await getAllPinpointAnswers();
    console.log(`✅ Found ${allAnswers.length} total answers`);
    
    // 测试获取最近答案
    const recentAnswers = await getRecentPinpointAnswers(5);
    console.log(`✅ Found ${recentAnswers.length} recent answers`);
    
    // 打印前几个答案的详细信息
    if (recentAnswers.length > 0) {
      console.log("📊 Recent answers sample:");
      recentAnswers.slice(0, 2).forEach((answer, index) => {
        console.log(`${index + 1}. Game #${answer.game_number}: ${answer.answer}`);
        console.log(`   Clues: ${[answer.clue_word_1, answer.clue_word_2, answer.clue_word_3, answer.clue_word_4, answer.clue_word_5].join(', ')}`);
        console.log(`   Date: ${answer.date}, Status: ${answer.status}`);
      });
    }
    
    return respData({
      success: true,
      totalAnswers: allAnswers.length,
      recentAnswers: recentAnswers.length,
      sampleData: recentAnswers.slice(0, 2).map(answer => ({
        gameNumber: answer.game_number,
        answer: answer.answer,
        clues: [answer.clue_word_1, answer.clue_word_2, answer.clue_word_3, answer.clue_word_4, answer.clue_word_5],
        date: answer.date,
        status: answer.status
      }))
    });
  } catch (error) {
    console.error("❌ Database test failed:", error);
    return respErr(`Database test failed: ${error}`);
  }
}
